// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get englishLanguage => 'English';

  @override
  String get dutchLanguage => 'Nederlands';

  @override
  String get title => 'Staff employee app';

  @override
  String get screen1 => '---> Welcome Screen <---';

  @override
  String get welcomeToStaff => 'Welcome to Staff!';

  @override
  String get knowDifferentName => 'Maybe you know us under a different name:';

  @override
  String get oneTimeActivation =>
      'This app requires one-time activation before it can be used.';

  @override
  String get easyQuick => 'But don\'t worry, it\'s easy and quick.';

  @override
  String get activate => 'Activate';

  @override
  String get switchToEnglish => 'Switch to english';

  @override
  String get screen2 => '---> Onboarding Screen <---';

  @override
  String get appActivation => 'App activation';

  @override
  String get appActivationDetailText =>
      'You can use the QR code that was sent to you by email to activate the app. You can also view this QR code on the \'Mijn profiel\' page on your PC.';

  @override
  String get appActivationStep1 => '1. Log in as you normally would';

  @override
  String get appActivationStep2 => '2. Go to \'Mijn profiel\'';

  @override
  String get appActivationStep3 => '3. Scan the QR-code or copy the url';

  @override
  String get swipeLeftToContinue => 'swipe left to continue';

  @override
  String get scanCode => 'Scan the code';

  @override
  String get pasteLink => 'Paste the link here';

  @override
  String get or => 'Or';

  @override
  String get scannerScreenText => 'Scan de QR code op de Mijin Profiel pagina';

  @override
  String get pleaseWait => 'Please wait';

  @override
  String get invalidLink => 'Invalid link.';

  @override
  String get login_screen => '------> login screen text  <------';

  @override
  String get userName => 'Username';

  @override
  String get passWord => 'Password';

  @override
  String get signIn => 'SIGN IN';

  @override
  String get scanNewCode => 'Scan new code';

  @override
  String get forgetPassword => 'Forgot password';

  @override
  String get enterUserNamePassword => 'Enter your username and password';

  @override
  String get inCorrectUserNamePassword => 'Incorrect username or password';

  @override
  String get invalidCredentialsText => 'Invalid credentials';

  @override
  String get forget_password_screen =>
      '------> forget password screen text  <------';

  @override
  String get passwordReset => 'Password reset';

  @override
  String get resetPassword => 'Reset password';

  @override
  String get forgetYourPassword => 'Forgot your password? no problem!';

  @override
  String get forgetPasswordText =>
      'Fill in your username below and you\'ll receive an email with instructions on how to get back into your account.';

  @override
  String get forgetErrorText =>
      'Something went wrong, please try again later or contact your IT support staff..';

  @override
  String get availabilityAppbarText => 'Availability';

  @override
  String get scheduleAppbarText => 'Schedule';

  @override
  String get hoursAppbarText => 'Hours';

  @override
  String get newsAppbarText => 'News';

  @override
  String get okUnderstood => 'OK understood';

  @override
  String get drawerTexts => '------> Drawer Text  <------';

  @override
  String get home => 'Home';

  @override
  String get schedule => 'Schedule';

  @override
  String get hours => 'Hours';

  @override
  String get availability => 'Availability';

  @override
  String get myProfile => 'My profile';

  @override
  String get signout => 'Sign out';

  @override
  String get about => 'About the app';

  @override
  String get news => 'News';

  @override
  String get absence => 'Absence';

  @override
  String get payslips => 'Payslips';

  @override
  String get settings => 'Settings';

  @override
  String get notifications => 'Notifications';

  @override
  String get clocking => 'Clocking';

  @override
  String get openServices => 'Open services';

  @override
  String get noNewNotificationText => 'No new notifications';

  @override
  String get poweredByStaffText => 'Powered by Staff Support';

  @override
  String get clocking_screen_text => '------> Clocking screen text  <------';

  @override
  String get clockingText => 'Clocking';

  @override
  String get departmentText => 'Department';

  @override
  String get activityText => 'Activity';

  @override
  String get clockInText => 'Clock in';

  @override
  String get clockOutText => 'Clock out';

  @override
  String get clockedSinceText => 'clocked in since: ';

  @override
  String get notClockedText => 'You are not clocked in';

  @override
  String get noDepartmentSelected => 'No department has been selected';

  @override
  String get noActivitySelectedText => 'No activity has been selected';

  @override
  String get errorText => 'Something went wrong';

  @override
  String get clockedInTime1 => 'You\'ve been clocked in for';

  @override
  String get clockedInTime2 => 'hours and';

  @override
  String get clockedInTime3 => 'minutes';

  @override
  String get clockingFailed =>
      'Clocking in failed. You can only clock in when your device is connected to a whitelisted (wifi) network.';

  @override
  String get profile_screen_text => '------> Profile screen text  <------';

  @override
  String get profileText => 'Profile';

  @override
  String get personalInformationText => 'Personal information';

  @override
  String get contactInformationText => 'Contact information';

  @override
  String get addressInformationText => 'Address information';

  @override
  String get changePasswordText => 'Change password';

  @override
  String get deleteAccountText => 'Delete Account';

  @override
  String get deleteAccountDetailsText =>
      'To delete your account and data mail to: <EMAIL>';

  @override
  String get supportEmail => '<EMAIL>';

  @override
  String get optionText => 'Options';

  @override
  String get cameraText => 'Take photo';

  @override
  String get selectPhotoText => 'Select photo';

  @override
  String get profile_information_screen_text =>
      '------> Profile information screen text  <------';

  @override
  String get firstNameText => 'First name';

  @override
  String get fullNameText => 'Full name';

  @override
  String get dateOfBirthText => 'Date of birth';

  @override
  String get bankAccountText => 'Bank account';

  @override
  String get change_password_screen_text =>
      '------> Change Password screen text  <------';

  @override
  String get currentPasswordText => 'Current password';

  @override
  String get newPasswordText => 'New password';

  @override
  String get confirmNewPasswordText => 'Confirm new password';

  @override
  String get saveButtonText => 'Save';

  @override
  String get changePasswordText2 => 'Change password';

  @override
  String get changePasswordInfoText =>
      'This password is valid for your Staff account, changing it here means changing your password for the web application as well.';

  @override
  String get passwordCantEmptyText => 'Suggestion or remark cannot be empty';

  @override
  String get passwordNotMatchedText => 'Passwords don\'t match';

  @override
  String get enterPasswordErrorText => 'Please enter password';

  @override
  String get passwordChangedText => 'Password changed';

  @override
  String get closeText => 'Close';

  @override
  String get payslip_screen_text => '------> Payslip screen text  <------';

  @override
  String get openingFileText => 'Opening file...';

  @override
  String get noDocumentAvailableText => 'No documents available';

  @override
  String get fileNotAvailableText => 'File not available';

  @override
  String get home_screen_text => '------> Home screen text  <------';

  @override
  String get summaryWeek => 'Summary';

  @override
  String get upComingShift => 'Upcoming shift';

  @override
  String get noShiftAvailable => 'No shift available';

  @override
  String get straightTo => 'Straight to';

  @override
  String get plannedShift => 'Planned shifts';

  @override
  String get workHour => 'Worked hours';

  @override
  String get latestNews => 'Latest news';

  @override
  String get noNewFound => 'No News Found';

  @override
  String get setting_screen_text => '------> Setting screen text <------';

  @override
  String get generalText => 'General';

  @override
  String get languageText => 'Language';

  @override
  String get cancelText => 'Cancel';

  @override
  String get dashBoardModeText => 'Dashboard mode';

  @override
  String get hourModeText => 'Hour mode';

  @override
  String get weekText => 'Week';

  @override
  String get monthText => 'Month';

  @override
  String get swipeDownOnDashboardText =>
      'Swipe down on the dashboard to reflect changes';

  @override
  String get securityText => 'Security';

  @override
  String get amountOfNewsText => 'Amount of news items';

  @override
  String get darkThemeText => 'Dark theme';

  @override
  String get privacyText => 'Privacy';

  @override
  String get shareAnalyticalDataText => 'Share analytical data';

  @override
  String get pin_screen_text => '------> Pin screen text <------';

  @override
  String get enterYourPinText => 'Enter your pin';

  @override
  String get usePinText => 'Use pin';

  @override
  String get usePinHelpInfoText =>
      'With a pin you can restrict access to the app and secure your data.';

  @override
  String get disablePinText => 'Disable pin';

  @override
  String get disablePinMessageText =>
      'Are you sure you want to disable your pin?';

  @override
  String get enterPinFirstTimeText => 'Enter a pin';

  @override
  String get enterPinSecondTimeText => 'Repeat the pin';

  @override
  String get pinSetText => 'Pin has been set';

  @override
  String get pinSetMessageText =>
      'You can now use this pin to sign into the app. You can always change this in the settings menu.';

  @override
  String get pinNotMatchText => 'Pins do not match';

  @override
  String get errorPinNotMatchText =>
      'The entered pins do not match. Please try again.';

  @override
  String get pinIncorrectText => 'Pin incorrect';

  @override
  String get pinIncorrectMessageText => 'The entered pin is incorrect.';

  @override
  String get forgotPinText => 'Forgot your pin?';

  @override
  String get forgotPinInfoText =>
      'You can reset your pin by login in using your username and password.';

  @override
  String get resetPinText => 'Reset pin';

  @override
  String get pinResetText => 'Pin reset';

  @override
  String get pinResetSuccessText =>
      'The pin has been reset. You can set a new pin in the settings menu.';

  @override
  String get availability_screen_text =>
      '------> availability screen text  <------';

  @override
  String get saved => 'Saved';

  @override
  String get availabilityErrorText =>
      'Somethig went wrong, please contact your IT service';

  @override
  String get remarkDot => 'Remark:';

  @override
  String get shifts => 'Shifts';

  @override
  String get availabilityDot => 'Availability:';

  @override
  String get startTime => 'Start time:';

  @override
  String get endTime => 'End time:';

  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get weekInformation => 'Week information';

  @override
  String get availabilityClosed => 'Availability closed';

  @override
  String get allAvailable => 'All available';

  @override
  String get allUnAvailable => 'All unavailable';

  @override
  String get from1 => 'From';

  @override
  String get untill1 => 'Untill';

  @override
  String get oK => 'OK';

  @override
  String get hour_screen_text => '------> hour screen text  <------';

  @override
  String get from => 'FROM';

  @override
  String get until => 'UNTIL';

  @override
  String get breakText => 'BREAK';

  @override
  String get total => 'TOTAL';

  @override
  String get department => 'Department';

  @override
  String get task => 'Task';

  @override
  String get selectedTask => 'Select task...';

  @override
  String get activities => 'Activities';

  @override
  String get timeSheet => 'Time sheets';

  @override
  String get addTimeSheet => 'Add time sheet';

  @override
  String get noTimeSheet => 'No time sheets';

  @override
  String get sheetNotSaved => 'Sheets with a * have not been saved';

  @override
  String get date => 'Date';

  @override
  String get closed => 'Closed';

  @override
  String get delete => 'Delete';

  @override
  String get deleteTimeSheet => 'Delete time sheet?';

  @override
  String get deleteConfirmText =>
      'Are you sure you want to delete this time sheet? This cannot be undone.';

  @override
  String get timeSheetRemove => 'Time sheet removed!';

  @override
  String get saving => 'Saving';

  @override
  String get selectActivity => 'Select an activity...';

  @override
  String get remarkRequiredError => 'Remark is required for this Activity';

  @override
  String get additional => 'Additional';

  @override
  String get lunch => 'Lunch';

  @override
  String get meal => 'Meal';

  @override
  String get surChargeService => 'Surcharge service 10%';

  @override
  String get workingFromHome => 'Working from home';

  @override
  String get remark => 'Remark';

  @override
  String get timeNotSet => 'Times are not set';

  @override
  String get savedSuccessful => 'Save successful';

  @override
  String get breakNotSelectable =>
      'You cannot select a break because your department has set it in advance';

  @override
  String get timeSheetFormError =>
      'The information is incorrect, would you please correct it?';

  @override
  String get januaryText => 'January';

  @override
  String get februaryText => 'February';

  @override
  String get marchText => 'March';

  @override
  String get aprilText => 'April';

  @override
  String get mayText => 'May';

  @override
  String get juneText => 'June';

  @override
  String get julyText => 'July';

  @override
  String get augustText => 'August';

  @override
  String get septemberText => 'September';

  @override
  String get octoberText => 'October';

  @override
  String get novemberText => 'November';

  @override
  String get decemberText => 'December';

  @override
  String get absence_screen_text => '------> absence screen text  <------';

  @override
  String get dateText => 'Date';

  @override
  String get beginText => 'Begin';

  @override
  String get structureText => 'Structure';

  @override
  String get withdrawalText => 'Withdrawal';

  @override
  String get balanceText => 'Balance';

  @override
  String get totalText => 'Total';

  @override
  String get vacationText => 'Vacation';

  @override
  String get myRequestText => 'My Request';

  @override
  String get createNewRequestText => 'Create New Request';

  @override
  String get saldoText => 'Balance';

  @override
  String get noDataFound => 'No data found!';

  @override
  String get leaveAdmissionsText => 'Leave admissions';

  @override
  String get newLeaveRequestText => 'New leave request';

  @override
  String get requestLeaveText => 'Request leave';

  @override
  String get startDateText => 'Start date';

  @override
  String get endDateText => 'End date';

  @override
  String get leaveTypeText => 'Leave type';

  @override
  String get workedHoursText => 'Worked hours';

  @override
  String get selectLeaveTypeText => 'Select leave type';

  @override
  String get leaveReasonText => 'Leave reason';

  @override
  String get submitLeaveText => 'Submit leave';

  @override
  String get someFieldsEmptyText => 'Some fields are empty';

  @override
  String get leaveReqSubmittedText => 'Leave request submitted';

  @override
  String get periodText => 'Period';

  @override
  String get selectPeriodText => 'Select a Period';

  @override
  String get leaveErrorMessage => 'Something went wrong, try again later';

  @override
  String get about_screen_text => '------> about screen text  <------';

  @override
  String get sendFeedbackText => 'Send feedback';

  @override
  String get problemExplanationText => 'Problem explanation';

  @override
  String get titleOrSubjectText => 'Title or subject';

  @override
  String get attachAnonymousDeviceInfo =>
      'Attach anonymous device info (optional)';

  @override
  String get doYouWantToReportText =>
      'Do you want to report a problem with this app? Please describe what went wrong as clear as possible and consider attaching a screenshot so our support staff can help you as fast as possible.';

  @override
  String get attachImageText => 'Attach image';

  @override
  String get attachmentText => 'Screenshot';

  @override
  String get appVersionLabel => 'App version';

  @override
  String get privacyPolicyLabel => 'Privacy policy';

  @override
  String get disclaimerLabel => 'Disclaimer';

  @override
  String get generalInformationLabel =>
      'De Staff employee app provides a quick and easy-to-use platform for both employees and employers to get information on the go.';

  @override
  String get webAppInformationLabel =>
      'The mobile app does not contain all the features the web application provides. Therefore, it is an addition to the all-around solution Staff provides for your company.';

  @override
  String get betaStateInformationLabel =>
      'The staff app is currently in beta state. Features are possibly incomplete and are always subject to change in the future.';

  @override
  String get thirdPartySoftwareLabel => 'Third-party software';

  @override
  String get feedbackSuccessMessage => 'Thank you for your feedback!';

  @override
  String get feedbackErrorMessage =>
      'Something went wrong, please try again later';

  @override
  String get titleText => 'Third-party software';

  @override
  String get generalInformationText =>
      'The staff app uses third-party software that requires us to show licenses. You can find these licenses below.';

  @override
  String get attachPersonalInfoLabel =>
      'Attach personal information so Staff can contact me if necessary. (optional)';

  @override
  String get sendFeedbackButtonLabel => 'Send feedback';

  @override
  String get sendButtonLabel => 'Send';

  @override
  String get licenseText =>
      'The MIT License (MIT)\n\nCopyright (c) 2023\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.';

  @override
  String get schedule_screen_text => '------> schedule screen text  <------';

  @override
  String get today => 'Today';

  @override
  String get day => 'Day';

  @override
  String get week => 'Week';

  @override
  String get month => 'Month';

  @override
  String get calendar => 'Calendar';

  @override
  String get downloadSchedule => 'Downloading schedule for ';

  @override
  String get departmentSchedule => 'Department schedule';

  @override
  String get noShifts => 'No shifts';

  @override
  String get download => 'Download';

  @override
  String get refresh => 'Refresh';

  @override
  String get startScheduleTime => 'Start time';

  @override
  String get breakScheduleText => 'Break';

  @override
  String get scheduleShift => 'Shifts';

  @override
  String get dayRemark => 'Day remark';

  @override
  String get remarkFromCollege => 'Comment from colleague';

  @override
  String get remarkFromManager => 'Manager\'s note';

  @override
  String get assignment => 'Assignment';

  @override
  String get rosterGroup => 'Roster group';

  @override
  String get costCenter => 'Cost centers';

  @override
  String get service => 'Service';

  @override
  String get noDepartment => 'No department';

  @override
  String get noScheduleAvailable => 'No schedule available';

  @override
  String get reflectChanges => 'Swipe down on the dashboard to reflect changes';

  @override
  String get noThanks => 'NO THANKS';

  @override
  String get setPin => 'SET PIN';

  @override
  String get setPinCode => 'Set PIN code';

  @override
  String get setPinMsg =>
      'To protect your data, we recommend that you set a PIN code.\n\nYou can always set a pin later in the settings.';

  @override
  String get openFile => 'Open file...';

  @override
  String get thankFeedBack => 'Thank you for your feedback!';

  @override
  String get thankFeedBackError =>
      'Something went wrong please try again later';

  @override
  String get subscribe => 'Subscribe';

  @override
  String get unsubscribe => 'Unsubscribe';

  @override
  String get subscribeSuccess => 'Subscribing successful';

  @override
  String get unsubscribeSuccess => 'Unsubscribing successful';

  @override
  String get swap_text => '------> swap text  <------';

  @override
  String get openShift => 'Open shifts';

  @override
  String get swapShift => 'Swap shift';

  @override
  String get cancelSwap => 'Cancel swap';

  @override
  String get requestToSwap => 'Request to swap has been submitted';

  @override
  String get swapWithdrawn => 'Swap request withdrawn';

  @override
  String get refuse => 'Refuse';

  @override
  String get registerShift => 'Register for shift';

  @override
  String get managerRemark => 'Manager\'s remark';

  @override
  String get registeredOnShift => 'You are registered on this shift';

  @override
  String get swapRequestRefused => 'Swap request refused';

  @override
  String get youHaveRegistered => 'You have registered for this shift';

  @override
  String get youAreAboutSwapShift => 'You\'re about to swap this shift';

  @override
  String get noteForColleagues => 'Note for your colleagues';

  @override
  String get noCancel => 'No, cancel';

  @override
  String get yesSwapShift => 'Yes, swap shift';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get leveRequest => 'Leave Request';

  @override
  String get start => 'Start';

  @override
  String get end => 'End';

  @override
  String get submittedOn => 'Submitted on';

  @override
  String get leaveType => 'Leave type';

  @override
  String get state => 'State';

  @override
  String get requestedHours => 'Requested hours';

  @override
  String get registeredHours => 'Registered hours';

  @override
  String get reason => 'Reason';

  @override
  String get declaration => 'Declaration';

  @override
  String get declarationsNotFound => 'Declarations not found!';

  @override
  String get overview => 'Overview';

  @override
  String get kilometers => 'Kilometers';

  @override
  String get bon => 'receipt';

  @override
  String get submitVoucher => 'New declaration';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get browsingPhone => 'Select from gallery';

  @override
  String get mileageRegistration => 'New mileage registration';

  @override
  String get newReceipt => 'New receipt';

  @override
  String get nameBean => 'Name of the bean';

  @override
  String get receiptSummary => 'Receipt summary';

  @override
  String get totalAmount => 'Total amount';

  @override
  String get receiptDate => 'Receipt date';

  @override
  String get description => 'Description';

  @override
  String get saveReceipt => 'Save receipt';

  @override
  String get confirm => 'Confirm';

  @override
  String get confirmDeclaration => 'Confirm declaration';

  @override
  String get notundoneDeclration =>
      'Once you have submitted this declaration, it cannot be undone.';

  @override
  String get goBack => 'Go back';

  @override
  String get declrationSave => 'Save';

  @override
  String get voucherSucessFully => 'Voucher successfully saved';

  @override
  String get numberofKiometers => 'Number of kilometers';

  @override
  String get cost => 'Costs';

  @override
  String get dateVoucher => 'Date voucher';

  @override
  String get saveRegistration => 'Submit declaration';

  @override
  String get enterKilometers => 'Please enter kilometers';

  @override
  String get enterTotalAmount => 'Please enter total amount';

  @override
  String get selectDate => 'Please select Date';

  @override
  String get enterDescription => 'Please enter description';

  @override
  String get ipAuthoriseError => 'Login is not allowed from this device';

  @override
  String get enterNameofBon => 'Please enter name of the Bon';

  @override
  String get typeToSearch => 'Type to search';
}
