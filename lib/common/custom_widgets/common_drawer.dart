import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/app/db/app_db.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_drawer_tiles.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/about_app_module/about_the_app_screen.dart';
import 'package:staff_medewerker/screens/absence_module/absence_screen.dart';
// import 'package:staff_medewerker/screens/balance_screen/balance_screen.dart';
import 'package:staff_medewerker/screens/authentication_module/login_module/ui/login_screen.dart';
import 'package:staff_medewerker/screens/clocking_module/clocking_screen.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/declaration_list_screen.dart';
import 'package:staff_medewerker/screens/notification_module/ui/notification_screen.dart';
import 'package:staff_medewerker/screens/open_service_module/ui/open_service_screen.dart';
import 'package:staff_medewerker/screens/payslip_module/ui/payslip_screen.dart';
import 'package:staff_medewerker/screens/profile_module/ui/profile_screen.dart';
import 'package:staff_medewerker/screens/settings_module/setting_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../screens/notification_module/bloc/notification_cubit.dart';
import '../../service/api_service/server_constants.dart';

class CommonDrawer extends StatelessWidget {
  final bool isFromAboutScreen;

  const CommonDrawer({
    super.key,
    this.isFromAboutScreen = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.primaryColor,
      child: SafeArea(
        bottom: false,
        child: Drawer(
          child: Container(
            color: context.themeColors.mediumBlackColor,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                Container(
                    padding: EdgeInsets.all(AppSize.sp13),
                    color: AppColors.primaryColor,
                    child: Image.asset(AssetsPath.mainWhiteLogo,
                        height: AppSize.h50, width: double.infinity)),
                Column(
                  children: [
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.home,
                      leadingIcon: Ionicons.home,
                      onTap: () {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        persistentTabController.jumpToTab(newIndex = 0);

                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.notifications,
                      leadingIcon: Ionicons.notifications,
                      trailing:
                          BlocBuilder<NotificationCubit, NotificationState>(
                        builder: (ctx, state) {
                          final notificationBloc =
                              ctx.read<NotificationCubit>();

                          if (notificationBloc.notificationList.isNotEmpty) {
                            return Container(
                              height: AppSize.w32,
                              width: AppSize.w32,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Color.fromRGBO(252, 238, 239, 1)),
                              child: Center(
                                  child: Text(
                                notificationBloc.notificationList.length
                                    .toString(),
                                style: context.textTheme.bodySmall?.copyWith(
                                    color: AppColors.lightModeRedColor,
                                    fontSize: AppSize.sp14),
                              )),
                            );
                          } else {
                            return Container(
                              width: 0,
                            );
                          }
                        },
                      ),
                      onTap: () async {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        await AppNavigation.nextScreen(
                            context, NotificationScreen());
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.schedule,
                      leadingIcon: Ionicons.calendar,
                      onTap: () async {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        persistentTabController.jumpToTab(newIndex = 2);
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.openServices,
                      isSvgIcon: true,
                      svgIconPath: AssetsPath.openServiceIcon,
                      iconColor: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.openServiceRights) ??
                              false
                          ? context.themeColors.darkGreyColor
                          : Color.fromRGBO(214, 214, 214, 1),
                      colorFilter: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.openServiceRights) ??
                              false
                          ? ColorFilter.mode(context.themeColors.darkGreyColor,
                              BlendMode.srcIn)
                          : ColorFilter.mode(Color.fromRGBO(186, 186, 186, 1),
                              BlendMode.srcIn),
                      titleStyle: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.openServiceRights) ??
                              false
                          ? context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal)
                          : context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal,
                              color: Color.fromRGBO(186, 186, 186, 1)),
                      onTap: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.openServiceRights) ??
                              false
                          ? () async {
                              if (isFromAboutScreen) {
                                AppNavigation.previousScreen(context);
                              }
                              await AppNavigation.nextScreen(
                                  context, OpenServicesScreen());
                              AppNavigation.previousScreen(context);
                            }
                          : null,
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.hours,
                      leadingIcon: Ionicons.time,
                      onTap: () {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        persistentTabController.jumpToTab(newIndex = 3);
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.availability,
                      leadingIcon: Ionicons.checkmark_circle,
                      onTap: () {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        persistentTabController.jumpToTab(newIndex = 1);
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.clocking,
                      leadingIcon: Ionicons.timer,
                      iconColor: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.clockingRights) ??
                              false
                          ? context.themeColors.darkGreyColor
                          : Color.fromRGBO(214, 214, 214, 1),
                      titleStyle: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.clockingRights) ??
                              false
                          ? context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal)
                          : context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal,
                              color: Color.fromRGBO(186, 186, 186, 1)),
                      onTap: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.clockingRights) ??
                              false
                          ? () async {
                              if (isFromAboutScreen) {
                                AppNavigation.previousScreen(context);
                              }
                              await AppNavigation.nextScreen(
                                  context, const ClockingScreen());
                              AppNavigation.previousScreen(context);
                            }
                          : null,
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.absence,
                      leadingIcon: Ionicons.sunny,
                      iconColor: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.leaveRights) ??
                              false
                          ? context.themeColors.darkGreyColor
                          : Color.fromRGBO(214, 214, 214, 1),
                      titleStyle: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.leaveRights) ??
                              false
                          ? context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal)
                          : context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal,
                              color: Color.fromRGBO(186, 186, 186, 1)),
                      onTap: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.leaveRights) ??
                              false
                          ? () async {
                              if (isFromAboutScreen) {
                                AppNavigation.previousScreen(context);
                              }
                              await AppNavigation.nextScreen(
                                  context, AbsenceScreen());
                              AppNavigation.previousScreen(context);
                            }
                          : null,
                    ),
                    // CustomDrawerListTile(
                    //   titleText: AppLocalizations.of(context)!.saldoText,
                    //   leadingIcon: Ionicons.wallet,
                    //   iconColor: appDB.user?.AppFunctionRightsList
                    //               ?.contains(AppConstants.leaveRights) ??
                    //           false
                    //       ? context.themeColors.darkGreyColor
                    //       : Color.fromRGBO(214, 214, 214, 1),
                    //   titleStyle: appDB.user?.AppFunctionRightsList
                    //               ?.contains(AppConstants.leaveRights) ??
                    //           false
                    //       ? context.textTheme.headlineLarge?.copyWith(
                    //           fontSize: AppSize.sp14,
                    //           fontWeight: FontWeight.normal)
                    //       : context.textTheme.headlineLarge?.copyWith(
                    //           fontSize: AppSize.sp14,
                    //           fontWeight: FontWeight.normal,
                    //           color: Color.fromRGBO(186, 186, 186, 1)),
                    //   onTap: appDB.user?.AppFunctionRightsList
                    //               ?.contains(AppConstants.leaveRights) ??
                    //           false
                    //       ? () async {
                    //           if (isFromAboutScreen) {
                    //             AppNavigation.previousScreen(context);
                    //           }
                    //           await AppNavigation.nextScreen(
                    //               context, BalanceScreen());
                    //           AppNavigation.previousScreen(context);
                    //         }
                    //       : null,
                    // ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.news,
                      leadingIcon: Ionicons.newspaper,
                      onTap: () {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        persistentTabController.jumpToTab(newIndex = 4);
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.payslips,
                      leadingIcon: Ionicons.document,
                      onTap: () async {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        await AppNavigation.nextScreen(
                            context, PaySlipScreen());
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      svgIconPath: AssetsPath.receiptIcon,
                      isSvgIcon: true,
                      iconColor: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.declarationRights) ??
                              false
                          ? context.themeColors.darkGreyColor
                          : Color.fromRGBO(214, 214, 214, 1),
                      titleStyle: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.declarationRights) ??
                              false
                          ? context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal)
                          : context.textTheme.headlineLarge?.copyWith(
                              fontSize: AppSize.sp14,
                              fontWeight: FontWeight.normal,
                              color: Color.fromRGBO(186, 186, 186, 1)),
                      onTap: appDB.user?.AppFunctionRightsList
                                  ?.contains(AppConstants.declarationRights) ??
                              false
                          ? () async {
                              if (isFromAboutScreen) {
                                AppNavigation.previousScreen(context);
                              }
                              await AppNavigation.nextScreen(
                                  context, DeclarationListScreen());
                              AppNavigation.previousScreen(context);
                            }
                          : null,
                      titleText: AppLocalizations.of(context)!.declaration,
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.myProfile,
                      leadingIcon: Ionicons.person,
                      onTap: () async {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        await AppNavigation.nextScreen(
                            context, ProfileScreen());
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    Container(
                      color: context.themeColors.darkBlackColor,
                      height: AppSize.h32,
                      width: double.infinity,
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.settings,
                      leadingIcon: Ionicons.settings,
                      onTap: () async {
                        if (isFromAboutScreen) {
                          AppNavigation.previousScreen(context);
                        }
                        await AppNavigation.nextScreen(
                            context, SettingScreen());
                        AppNavigation.previousScreen(context);
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.signout,
                      leadingIcon: Ionicons.log_out,
                      onTap: () async {
                        AppNavigation.previousScreen(context);

                        Prefs.preferences.clear();
                        AppDB appDB = await AppDB.getInstance();
                        await appDB.clearData();
                        persistentTabController.jumpToTab(newIndex = 0);
                        await prefs.setString(
                            'base_url', ServerConstant.base_url);
                        print("apppp: ${appDB.user?.APIKey}");

                        await AppNavigation.pushAndRemoveAllScreen(
                            context, LoginScreen());
                      },
                    ),
                    CustomDrawerListTile(
                      titleText: AppLocalizations.of(context)!.about,
                      leadingIcon: Ionicons.information_circle_outline,
                      onTap: () async {
                        if (!isFromAboutScreen) {
                          await AppNavigation.nextScreen(
                              context, const AboutTheAppScreen());
                        }

                        AppNavigation.previousScreen(context);
                      },
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    AppLocalizations.of(context)!.poweredByStaffText,
                    style: context.textTheme.headlineLarge?.copyWith(
                        fontSize: AppSize.sp8, fontWeight: FontWeight.normal),
                  ),
                ),
                // SpaceV(AppSize.h50)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
