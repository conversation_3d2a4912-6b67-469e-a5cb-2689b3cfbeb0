import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/widgets/custom_delcaration_textfield.dart';
import 'package:staff_medewerker/screens/declaration_module/widgets/select_date_picker_bottom_sheet.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';

import '../../../main.dart';

class MileageRegisterScreen extends StatefulWidget {
  final String guid;
  MileageRegisterScreen({super.key, required this.guid});

  @override
  State<MileageRegisterScreen> createState() => _MileageRegisterScreenState();
}

class _MileageRegisterScreenState extends State<MileageRegisterScreen> {
  FocusNode _focusNode = FocusNode();

  FocusNode _focusNode1 = FocusNode();

  final GlobalKey<FormState> _formKey = GlobalKey();

  @override
  void initState() {
    final blocProvider = BlocProvider.of<DeclrationCubit>(context);
    blocProvider.clearData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeclrationCubit, DeclrationState>(
      builder: (ctx, state) {
        final ref = ctx.read<DeclrationCubit>();

        if (state is DeclrationUdateDate)
          log('confirmDate ${state.confirmDate}');
        return Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor:
              context.themeColors.declarativeScaffoldBackgroundColor,
          appBar: CustomAppBar(
              title: AppLocalizations.of(context)!.mileageRegistration),
          body: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(AppSize.sp20),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    //  color: Color.fromRGBO(255, 253, 253, 1),
                    color: context.themeColors.declarativeBackgroundColor,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: AppSize.w20, vertical: AppSize.h10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        // mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpaceV(AppSize.h30),
                          Text(
                              AppLocalizations.of(context)!.mileageRegistration,
                              style: context.textTheme.titleMedium?.copyWith()),
                          SpaceV(AppSize.h20),
                          Text(AppLocalizations.of(context)!.numberofKiometers,
                              style: context.textTheme.titleMedium
                                  ?.copyWith(fontSize: AppSize.sp13)),
                          SpaceV(AppSize.h10),
                          CustomDelcarationTextfield(
                            controller: ref.nameController,
                            keyboardType: TextInputType.number,
                            labelText: AppLocalizations.of(context)!.kilometers,
                            validateMsg:
                                AppLocalizations.of(context)!.enterKilometers,
                            onChange: (value) {
                              final totalkm = double.tryParse(
                                      ref.nameController.text.toString()) ??
                                  0.0;
                              final totalCost = totalkm * 0.23;
                              if (ref.nameController.text.isEmpty) {
                                ref.totalAmountController.clear();
                              } else {
                                ref.totalAmountController.text =
                                    totalCost.toStringAsFixed(2);
                              }
                            },
                          ),
                          SpaceV(AppSize.h20),
                          Text(AppLocalizations.of(context)!.declaration,
                              style: context.textTheme.titleMedium
                                  ?.copyWith(fontSize: AppSize.sp13)),
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            enable: false,
                            keyboardType: TextInputType.number,
                            focusNode: _focusNode,
                            controller: ref.totalAmountController,
                            readOnly: true,
                            labelText: AppLocalizations.of(context)!.cost,
                            validateMsg:
                                AppLocalizations.of(context)!.enterTotalAmount,
                          ),
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            controller: ref.dateController,
                            labelText: AppLocalizations.of(context)!.date,
                            readOnly: true,
                            validateMsg:
                                AppLocalizations.of(context)!.selectDate,
                            onTap: () {
                              DatePickerBottomSheet.selectDateFromCalendar(
                                  context, ref, _focusNode, () {
                                ref.updateDate(ref.selectedDay.value);
                                ref.dateController.text =
                                    DateFormat('dd MMM yyyy')
                                        .format(ref.selectedDay.value);
                                debugPrint(
                                    '${(state is DeclrationUdateDate) ? state.confirmDate : null}');
                                Navigator.pop(context);
                              });
                            },
                          ),
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            maxLines: 2,
                            controller: ref.descriptionController,
                            focusNode: _focusNode1,
                            labelText:
                                AppLocalizations.of(context)!.description,
                            validateMsg:
                                AppLocalizations.of(context)!.enterDescription,
                          ),
                          SpaceV(AppSize.h30),
                          Padding(
                            padding:
                                EdgeInsets.symmetric(horizontal: AppSize.w60),
                            child: ReusableContainerButton(
                                elevation: 0,
                                height: AppSize.h36,
                                borderRadius:
                                    BorderRadius.circular(AppSize.r24),
                                onPressed: () {
                                  var form = _formKey.currentState!;
                                  _focusNode.unfocus();
                                  _focusNode1.unfocus();
                                  if (form.validate()) {
                                    form.save();
                                    DatePickerBottomSheet.confirmVoucherSheet(
                                        context, _focusNode, _focusNode1, () {
                                      _focusNode.unfocus();
                                      _focusNode1.unfocus();

                                      Map<String, dynamic> bodyData = {
                                        "APIKeyLogin": {
                                          "DeviceId": "",
                                          "APIKey": APIKey
                                        },
                                        "Date":
                                            ref.selectedDay.value.toString(),
                                        "TypeId": widget.guid,
                                        "Kilometers":
                                            ref.nameController.text.toString(),
                                        "Costs": ref.totalAmountController.text
                                            .toString(),
                                        "Description": ref
                                            .descriptionController.text
                                            .toString(),
                                        "ProjectTaskId": widget.guid,
                                      };

                                      log('data ====>${bodyData}');

                                      ref.getCreateDeclarationData(
                                          context: context, data: bodyData);
                                    });
                                  } else {
                                    return null;
                                  }
                                },
                                buttonText: AppLocalizations.of(context)!
                                    .saveRegistration,
                                textStyle: context.textTheme.bodySmall
                                    ?.copyWith(
                                        color: Colors.white,
                                        fontSize: AppSize.sp13,
                                        fontWeight: FontWeight.w500)),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
    ;
  }
}
