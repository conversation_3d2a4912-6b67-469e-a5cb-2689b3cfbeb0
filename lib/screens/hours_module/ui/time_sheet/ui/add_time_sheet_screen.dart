import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_listtile.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_loader.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_switch.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/bloc/time_sheet_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/activity_screen/activity_list_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/activity_screen/bloc/activity_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/department_screen/bloc/department_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/department_screen/department_list_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/task_screen/bloc/task_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/task_screen/task_list_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/widgets/from_until_break_time_picker/time_picker.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/widgets/time_picker_widget/time_picker.dart'
    as activityTime;
import 'package:staff_medewerker/screens/hours_module/widget/add_time_container.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class AddTimeSheetScreen extends StatefulWidget {
  final String appBarTitle;
  final int index;
  final String selectedDate;
  final bool isFromEdit;

  AddTimeSheetScreen(
      {Key? key,
      required this.appBarTitle,
      required this.index,
      required this.selectedDate,
      this.isFromEdit = false})
      : super(key: key);

  @override
  State<AddTimeSheetScreen> createState() => _AddTimeSheetScreenState();
}

class _AddTimeSheetScreenState extends State<AddTimeSheetScreen> {
  int selectedIndex = 1;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    final timeBloc = BlocProvider.of<TimeSheetCubit>(context);

    // print(
    //     "timeBloc.myTimeSheetList.value[widget.index].tb1Remark2 00 ${timeBloc.myTimeSheetList.value[widget.index].breakTime}");
    timeBloc.index = widget.index;

    timeBloc.clearAllData(widget.isFromEdit);

    timeBloc.remarkTextController.text =
        timeBloc.myTimeSheetList.value[widget.index].remark ?? "";
    timeBloc.remarkTextController1.text =
        timeBloc.myTimeSheetList.value[widget.index].tb1Remark ?? "";
    timeBloc.remarkTextController2.text =
        timeBloc.myTimeSheetList.value[widget.index].tb2Remark ?? "";
    timeBloc.remarkTextController3.text =
        timeBloc.myTimeSheetList.value[widget.index].tb3Remark ?? "";

    timeBloc.remarkValidate1 =
        timeBloc.myTimeSheetList.value[widget.index].tb1RemarkRequired ?? false;
    timeBloc.remarkValidate2 =
        timeBloc.myTimeSheetList.value[widget.index].tb2RemarkValidate ?? false;
    timeBloc.remarkValidate3 =
        timeBloc.myTimeSheetList.value[widget.index].tb3RemarkValidate ?? false;

    timeBloc.myTimeSheetList.value[widget.index].isError = false;
    //
    context.read<DepartmentCubit>().departmentList.forEach((element) {
      if (element.guid ==
          timeBloc.myTimeSheetList.value[widget.index].departmentId) {
        timeBloc.selectedDepartmentName = element.title;
        timeBloc.selectedDepartmentId = element.guid;
      }
    });

    context.read<TaskCubit>().taskList.forEach((element) {
      if (element.calendarEntryId ==
          timeBloc.myTimeSheetList.value[widget.index].calendarEntryId) {
        timeBloc.selectedTaskName = element.relation;
        timeBloc.selectedTaskId = element.calendarEntryId;
      }
    });

    widget.isFromEdit
        ? context.read<ActivityCubit>().activityList.forEach((element) {
            if (element.costCenterId ==
                timeBloc.myTimeSheetList.value[widget.index].tb1CostCenterId) {
              print(
                  "costCenterIdtimeBloc.selectedActivityName3: ${element.costCenterId}");
              print(
                  "costCenterIdtimeBloc.selectedActivityName3: ${timeBloc.myTimeSheetList.value[widget.index].tb1CostCenterId}");
              timeBloc.selectedActivityId1 = element.costCenterId;
              timeBloc.selectedActivityName1 = element.title;
            }

            if (element.costCenterId ==
                timeBloc.myTimeSheetList.value[widget.index].tb2CostCenterId) {
              timeBloc.selectedActivityId2 = element.costCenterId;
              timeBloc.selectedActivityName2 = element.title;
            }

            if (element.costCenterId ==
                timeBloc.myTimeSheetList.value[widget.index].tb3CostCenterId) {
              timeBloc.selectedActivityId3 = element.costCenterId;
              timeBloc.selectedActivityName3 = element.title;
            }
          })
        : null;

    print("timeBloc.selectedActivityName3: ${timeBloc.selectedActivityName2}");
    print("timeBloc.selectedActivityName3: ${timeBloc.selectedActivityName3}");
    timeBloc.calculateTotalOfFromUntilBreakTime(context);

    timeBloc.fetchBreakTime(context);
    timeBloc.calculateTotalOfFromUntilBreakTime(context);
    timeBloc.checkRemarkRequiredOrNot(context: context);
  }

  @override
  Widget build(BuildContext context) {
    final timeBloc = context.read<TimeSheetCubit>();
    print("taskList.length: ${context.read<TaskCubit>().taskList.length}");
    print("timeBloc.index ${timeBloc.getDepartmentSettingList[0]}");

    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
          title: "",
          centerTitle: true,
          leading: Padding(
            padding: EdgeInsets.only(left: AppSize.w12),
            child: BlocBuilder<TimeSheetCubit, TimeSheetState>(
              buildWhen: (previous, current) {
                return widget.index <=
                    (timeBloc.myTimeSheetList.value.length - 1);
              },
              builder: (ctx, state) {
                return Row(
                  children: [
                    Text(widget.appBarTitle,
                        style: context.textTheme.headlineLarge!.copyWith(
                            color: Colors.white,
                            fontSize: AppSize.sp17,
                            fontWeight: FontWeight.w500)),
                    SpaceH(AppSize.w2),
                    (timeBloc.myTimeSheetList.value[0].editRight ?? false)
                        ? Container()
                        : Icon(Ionicons.lock_closed, size: AppSize.sp20)
                  ],
                );
              },
            ),
          ),
          actions: true,
          isLeading: true,
          leadingWidth: AppSize.w250,
          actionList: [
            PopupMenuButton(
              icon: Icon(Icons.more_vert_rounded, color: AppColors.white),
              offset: Offset(0.0, AppBar().preferredSize.height * 0.9),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppSize.r4),
              ),
              color: context.themeColors.listGridColor1,
              itemBuilder: (context) {
                return <PopupMenuEntry<int>>[
                  PopupMenuItem<int>(
                    padding:
                        EdgeInsets.only(right: AppSize.w100, left: AppSize.w12),
                    value: 0,
                    child: Row(
                      children: [
                        Icon(
                          Ionicons.trash_sharp,
                          color: context.themeColors.textColor,
                        ),
                        SpaceH(AppSize.w10),
                        Text(AppLocalizations.of(context)!.delete,
                            textAlign: TextAlign.center,
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp15,
                            )),
                      ],
                    ),
                  ),
                ];
              },
              onSelected: (value) async {
                {
                  await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (deleteDialogContext) {
                      return CustomAlertDialog(
                          context: context,
                          cancelText: AppLocalizations.of(context)!
                              .closeText
                              .toUpperCase(),
                          message:
                              AppLocalizations.of(context)!.deleteConfirmText,
                          onOkPressed: () async {
                            if (timeBloc.newItemStartList[widget.index] !=
                                " *") {
                              bool isError = false;

                              for (int i = 0;
                                  i < timeBloc.myTimeSheetList.value.length;
                                  i++) {
                                final element =
                                    timeBloc.myTimeSheetList.value[i];
                                print(
                                    'element.totalHours != element.totalActivityHours${element.totalHours}');
                                print(
                                    'element.totalHours != element.totalActivityHours${element.totalActivityHours}');
                                if (element.tb1Hours == "00:00") {
                                  element.tb1Hours = null;
                                }
                                if (element.tb2Hours == "00:00") {
                                  element.tb2Hours = null;
                                }
                                if (element.tb3Hours == "00:00") {
                                  element.tb3Hours = null;
                                }

                                if (element.timeFrom == null ||
                                    element.timeUntil == null) {
                                  isError = true;
                                  element.isError = true;
                                  customSnackBar(
                                      context: context,
                                      message: AppLocalizations.of(context)!
                                          .timeNotSet);
                                  Navigator.pop(deleteDialogContext);

                                  break;
                                } else if ((element.tb1Hours != null &&
                                        element.tb1CostCenterId == null) ||
                                    (element.tb2Hours != null &&
                                        element.tb2CostCenterId == null) ||
                                    (element.tb3Hours != null &&
                                        element.tb3CostCenterId == null)) {
                                  element.isError = true;
                                  isError = true;
                                  customSnackBar(
                                      context: context,
                                      message: AppLocalizations.of(context)!
                                          .noActivitySelectedText);
                                  break;
                                } else if ((element.tb1RemarkRequired == true &&
                                        element.tb1Remark == "") ||
                                    (element.tb2RemarkValidate == true &&
                                        element.tb2Remark == "") ||
                                    (element.tb3RemarkValidate == true &&
                                        element.tb3Remark == "")) {
                                  element.isError = true;
                                  isError = true;
                                  customSnackBar(
                                      context: context,
                                      message: AppLocalizations.of(context)!
                                          .passwordCantEmptyText);
                                  break;
                                } else if (element.totalHours !=
                                    element.totalActivityHours) {
                                  element.isError = true;
                                  isError = true;
                                  customSnackBar(
                                      context: context,
                                      message: AppLocalizations.of(context)!
                                          .timeSheetFormError);
                                  break;
                                }
                              }

                              if (!isError) {
                                for (int i = 0;
                                    i < timeBloc.myTimeSheetList.value.length;
                                    i++) {
                                  if (i < timeBloc.newItemStartList.length &&
                                      timeBloc.newItemStartList[i]
                                          .contains(" *")) {
                                    timeBloc.myTimeSheetList.value[i].command =
                                        "INSERT";
                                  } else {
                                    timeBloc.myTimeSheetList.value[i].command =
                                        "UPDATE";
                                  }
                                }

                                timeBloc.myTimeSheetList.value[widget.index]
                                    .command = "DELETE";
                                String myTimeSheetListJson =
                                    json.encode(timeBloc.myTimeSheetList.value);
                                log(myTimeSheetListJson);
                                Navigator.pop(deleteDialogContext);

                                // String myTimeSheetListJson = json.encode(timeBloc.myTimeSheetList.value);
                                // log(myTimeSheetListJson);
                                //  Navigator.pop(deleteDialogContext);

                                Loader.showLoaderDialog(
                                    navigatorKey.currentContext!);
                                await timeBloc.myTimesheetDataSaveApi(
                                    context: context,
                                    myTimeSheetListJson: myTimeSheetListJson);
                                Loader.closeLoadingDialog(
                                    navigatorKey.currentContext!);
                              }
                            } else {
                              Navigator.pop(deleteDialogContext);
                              AppNavigation.previousScreen(context);
                              // deleteData(timeBloc);
                              timeBloc.myTimeSheetList.value
                                  .removeAt(widget.index);
                              timeBloc.newItemStartList.removeAt(widget.index);
                            }
                            // timeBloc.rebuildScreen();
                          },
                          title: AppLocalizations.of(context)!.deleteTimeSheet,
                          isCancelButton: true,
                          okButtonText: AppLocalizations.of(context)!.delete);
                    },
                  );
                }
              },
            ),
          ]),
      body: BlocBuilder<TimeSheetCubit, TimeSheetState>(
        buildWhen: (previous, current) {
          return widget.index <= (timeBloc.myTimeSheetList.value.length - 1);
        },
        builder: (ctx, state) {
          final departmentBloc = BlocProvider.of<DepartmentCubit>(context);
          print(
              "previous value =====>${timeBloc.myTimeSheetList.value.length}");
          List<MyTimeSheetResponseModel> timeSheetList =
              timeBloc.myTimeSheetList.value;
          print(
              "timeBloc.myTimeSheetList.value[widget.index].tb1Remark2${timeSheetList[widget.index].breakTime}");
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: AppSize.w14),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SpaceV(AppSize.h20),
                  FittedBox(
                    child: Row(
                      children: [
                        AddTimeContainerWidget(
                          titleText: AppLocalizations.of(context)!.from,
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: context.themeColors.drawerColor,
                              builder: (BuildContext context) {
                                return TimeSheetTimePicker(
                                  onOkPressed: (String value) {
                                    print(value);
                                    timeBloc.selectFromTime(
                                        context, widget.index,
                                        fromTime: value);
                                  },
                                  initialTime:
                                      timeSheetList[widget.index].timeFrom ??
                                          "00:00",
                                );
                              },
                            );

                            // timeBloc.selectFromTime(context, widget.index);
                          },
                          titleValue: timeSheetList[widget.index].timeFrom,
                        ),
                        SpaceH(AppSize.w10),
                        AddTimeContainerWidget(
                          titleText: AppLocalizations.of(context)!.until,
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: context.themeColors.drawerColor,
                              builder: (BuildContext context) {
                                return TimeSheetTimePicker(
                                  onOkPressed: (String value) {
                                    print(value);
                                    timeBloc.selectUntilTime(
                                        context, widget.index,
                                        untilTime: value);
                                    // timeBloc.myTimeSheetList.value[widget.index].timeFrom = value;
                                    timeBloc.checkTotalAndActivityTime();
                                  },
                                  initialTime:
                                      timeSheetList[widget.index].timeUntil ??
                                          "00:00",
                                );
                              },
                            );

                            // timeBloc.selectUntilTime(context, widget.index);
                          },
                          titleValue: timeSheetList[widget.index]
                                      .timeUntil
                                      ?.isNotEmpty ??
                                  false
                              ? timeSheetList[widget.index].timeUntil
                              : '00:00',
                        ),
                        SpaceH(AppSize.w10),
                        ValueListenableBuilder(
                          valueListenable: timeBloc.isBreakSelectable,
                          builder: (context2, isBreakSelectable, child) {
                            log("timeSheetList[widget.index].breakTime 2323 ${timeSheetList[widget.index].breakTime}");
                            return AddTimeContainerWidget(
                                color: isBreakSelectable
                                    ? AppColors.primaryColor
                                    : AppColors.primaryColor.withOpacity(0.5),
                                titleText:
                                    AppLocalizations.of(context)!.breakText,
                                // onTap: isBreakSelectable
                                //     ? () {
                                //         showModalBottomSheet(
                                //           context: context,
                                //           builder: (BuildContext context) {
                                //             return TimeSheetTimePicker(
                                //               onOkPressed: (String value) {
                                //                 print(value);
                                //                 timeBloc.selectBreakTime(context, breakTime: value);
                                //                 // timeBloc.myTimeSheetList.value[widget.index].timeFrom = value;
                                //                 timeBloc.checkTotalAndActivityTime();
                                //               },
                                //               initialTime: timeSheetList[widget.index].breakTime ?? "00:00",
                                //             );
                                //           },
                                //         );
                                //
                                //         // timeBloc.selectBreakTime(context);
                                //       }
                                //     : (){
                                //   PopupMenuButton(
                                //     tooltip: "",
                                //     splashRadius: 1,
                                //     elevation: 20,
                                //     iconSize: 50,
                                //     offset: Offset(10, 20),
                                //     child: Container(
                                //       padding: EdgeInsets.only(
                                //         bottom: AppSize.sp3,
                                //         left: AppSize.sp3,
                                //       ),
                                //       alignment: Alignment.centerRight,
                                //       child: Icon(
                                //         Ionicons.help_circle_outline,
                                //         size: AppSize.sp14,
                                //         color: context.themeColors.textColor,
                                //       ),
                                //     ),
                                //     color: context.themeColors.cardColor,
                                //     itemBuilder: (context) => [
                                //       PopupMenuItem(
                                //         child: Text(AppLocalizations.of(context)!.usePinHelpInfoText,
                                //             softWrap: true,
                                //             style: context.textTheme.bodyMedium
                                //                 ?.copyWith(fontSize: AppSize.sp14, color: context.themeColors.textColor)),
                                //         enabled: false,
                                //       ),
                                //     ],
                                //     // splashRadius: 1,
                                //   ),
                                //
                                // }
                                onTap: isBreakSelectable
                                    ? () {
                                        showModalBottomSheet(
                                          context: context,
                                          backgroundColor:
                                              context.themeColors.drawerColor,
                                          builder: (BuildContext context) {
                                            return TimeSheetTimePicker(
                                              onOkPressed: (String value) {
                                                print(value);
                                                timeBloc.selectBreakTime(
                                                    context,
                                                    breakTime: value);
                                                log("value value $value");
                                                // timeBloc.myTimeSheetList.value[widget.index].timeFrom = value;
                                                timeBloc
                                                    .checkTotalAndActivityTime();
                                              },
                                              initialTime:
                                                  timeSheetList[widget.index]
                                                          .breakTime ??
                                                      "00:00",
                                            );
                                          },
                                        );
                                      }
                                    : () {
                                        showMenu(
                                          color: context
                                              .themeColors.listGridColor1,
                                          context: context,
                                          position: RelativeRect.fromLTRB(
                                              10, 150, 0, 20),
                                          items: [
                                            PopupMenuItem(
                                              child: Text(
                                                AppLocalizations.of(context)!
                                                    .breakNotSelectable,
                                                softWrap: true,
                                                style: context
                                                    .textTheme.bodyMedium
                                                    ?.copyWith(
                                                        fontSize: AppSize.sp14,
                                                        color: context
                                                            .themeColors
                                                            .textColor),
                                              ),
                                              enabled: false,
                                            ),
                                          ],
                                        );
                                      },
                                titleValue: timeSheetList[widget.index]
                                            .breakTime
                                            ?.isNotEmpty ??
                                        true
                                    ? timeSheetList[widget.index].breakTime
                                    : '00:00');
                          },
                        ),
                        SpaceH(AppSize.w10),
                        ValueListenableBuilder(
                          valueListenable: timeBloc.totalSelectedTime,
                          builder: (context, value, child) {
                            print("total ======>${value}");
                            return AddTimeContainerWidget(
                              titleText: AppLocalizations.of(context)!.total,
                              borderRadius: 0,
                              color: context.themeColors.listGridColor1,
                              titleValue: value,
                              titleValueStyle:
                                  context.textTheme.titleMedium?.copyWith(
                                color: AppColors.primaryColor,
                                fontSize: AppSize.sp14,
                              ),
                              titleTextStyle:
                                  context.textTheme.titleMedium?.copyWith(
                                color: AppColors.primaryColor,
                                fontSize: AppSize.sp14,
                              ),
                            );
                          },
                        ),
                        SpaceH(AppSize.w10),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () async {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => DepartmentScreen(
                                  selectedDepartmentValue: timeBloc
                                          .selectedDepartmentName ??
                                      departmentBloc.departmentList[0].title,
                                  selectedDepartmentId:
                                      timeBloc.selectedDepartmentId ??
                                          departmentBloc.departmentList[0].guid,
                                )),
                      );
                      if (result != null) {
                        timeBloc.updateDepartmentValue(
                            result['selectedDepartmentName'],
                            result['selectedDepartmentId']);
                      }

                      await context.read<TimeSheetCubit>().getDepartmentSetting(
                          context: context,
                          guid: result['selectedDepartmentId']);

                      timeBloc.fetchBreakTime(context);
                      timeBloc.myTimeSheetList.value[widget.index]
                          .departmentId = result['selectedDepartmentId'];

                      timeBloc.calculateTotalOfFromUntilBreakTime(context);
                      timeBloc.checkRemarkRequiredOrNot(context: context);
                    },
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: AppSize.h18,
                          bottom: AppSize.h12,
                          left: AppSize.w14,
                          right: AppSize.w14),
                      child: Container(
                        child: Row(
                          children: [
                            Text(
                              AppLocalizations.of(context)!.departmentText,
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.themeColors.textColor,
                                fontSize: AppSize.sp15,
                              ),
                            ),
                            Spacer(),
                            Row(
                              children: [
                                Text(
                                  timeBloc.selectedDepartmentName ??
                                      departmentBloc.departmentList[0].title,
                                  // timeBloc.selectedDepartmentName ??"",
                                  style:
                                      context.textTheme.headlineLarge?.copyWith(
                                    fontSize: AppSize.sp14,
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                                SpaceH(AppSize.w4),
                                Icon(
                                  Ionicons.caret_down,
                                  color: context.themeColors.iconColor,
                                  size: AppSize.sp12,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  context.read<TaskCubit>().taskList.length == 0
                      ? Container()
                      : InkWell(
                          onTap: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TaskScreen(
                                        selectedTaskValue:
                                            timeBloc.selectedTaskName ??
                                                AppLocalizations.of(context)!
                                                    .selectedTask,
                                        selectedTaskId:
                                            timeBloc.selectedTaskId ?? "",
                                      )),
                            );
                            if (result != null) {
                              timeBloc.updateTaskValue(
                                  result['selectedTaskName'],
                                  result['selectedTaskId']);
                            }

                            timeBloc.myTimeSheetList.value[widget.index]
                                .calendarEntryId = result['selectedTaskId'];
                          },
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: AppSize.h10,
                                bottom: AppSize.h12,
                                left: AppSize.w14,
                                right: AppSize.w14),
                            child: Container(
                              child: Row(
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.task,
                                    style:
                                        context.textTheme.bodyMedium?.copyWith(
                                      color: context.themeColors.textColor,
                                      fontSize: AppSize.sp15,
                                    ),
                                  ),
                                  Spacer(),
                                  Row(
                                    children: [
                                      Text(
                                        timeBloc.selectedTaskName ??
                                            AppLocalizations.of(context)!
                                                .selectedTask,
                                        // timeBloc.selectedDepartmentName ??"",
                                        style: context.textTheme.headlineLarge
                                            ?.copyWith(
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.normal,
                                        ),
                                      ),
                                      SpaceH(AppSize.w4),
                                      Icon(
                                        Ionicons.caret_down,
                                        color: context.themeColors.iconColor,
                                        size: AppSize.sp12,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                  Container(
                    height: AppSize.h34,
                    color: context.themeColors.listGridColor1,
                    padding: EdgeInsets.symmetric(horizontal: AppSize.w14),
                    alignment: Alignment.centerLeft,
                    child: Text(AppLocalizations.of(context)!.selectActivity,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.themeColors.textColor,
                          fontSize: AppSize.sp14,
                        )),
                  ),
                  Container(
                    color: (timeSheetList[widget.index].isError ?? false)
                        ? context.themeColors.buttonRedColor
                        : null,
                    child: Column(
                      children: [
                        // activity 1
                        Padding(
                          padding: EdgeInsets.only(
                            top: AppSize.h10,
                            left: AppSize.w14,
                            right: AppSize.w14,
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    backgroundColor:
                                        context.themeColors.drawerColor,
                                    builder: (BuildContext context) {
                                      return activityTime.TimeSheetTimePicker(
                                        onOkPressed: (String value) {
                                          print(value);
                                          timeSheetList[widget.index].tb1Hours =
                                              value;
                                          print(
                                              "timeSheetList[widget.index].tb1Hours${timeSheetList[widget.index].tb1Hours}");
                                          timeBloc.checkTotalAndActivityTime();
                                        },
                                        selectedTime:
                                            timeSheetList[widget.index]
                                                    .tb1Hours ??
                                                "00:00",
                                        initialTime: timeSheetList[widget.index]
                                                .tb1Hours ??
                                            "00:00",
                                      );
                                    },
                                  );
                                },
                                child: Text(
                                  timeBloc.myTimeSheetList.value[widget.index]
                                          .tb1Hours ??
                                      '00:00',
                                  style: context.textTheme.bodyMedium?.copyWith(
                                      color:
                                          timeSheetList[widget.index].isError ??
                                                  false
                                              ? AppColors.white
                                              : context.themeColors.textColor,
                                      fontWeight: FontWeight.w500,
                                      fontSize: AppSize.sp14,
                                      letterSpacing: 1),
                                ),
                              ),
                              Spacer(),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      final activityBloc =
                                          BlocProvider.of<ActivityCubit>(
                                              context);

                                      final result = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              ActivitySearchListScreen(
                                            selectedActivityName:
                                                timeBloc.selectedActivityName1,
                                            selectedActivityId: timeBloc
                                                    .myTimeSheetList
                                                    .value[widget.index]
                                                    .tb1CostCenterId ??
                                                activityBloc.activityList[0]
                                                    .costCenterId,
                                            activityIndex: 1,
                                          ),
                                        ),
                                      );

                                      if (result != null) {
                                        timeBloc.updateActivityValue1(
                                            result['selectedValue'],
                                            result['selectedActivityId']);
                                      }

                                      timeBloc
                                              .myTimeSheetList
                                              .value[widget.index]
                                              .tb1CostCenterId =
                                          result['selectedActivityId'];

                                      timeBloc.checkRemarkRequiredOrNot(
                                          context: context);
                                    },
                                    child: Text(
                                      timeBloc.selectedActivityName1 ==
                                              "Activity"
                                          ? AppLocalizations.of(context)!
                                              .activityText
                                          : timeBloc.selectedActivityName1,
                                      style: context.textTheme.headlineLarge
                                          ?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.normal,
                                        color: timeBloc.selectedActivityName1 ==
                                                "Activity"
                                            ? context.themeColors.textColor
                                                .withOpacity(0.4)
                                            : timeSheetList[widget.index]
                                                        .isError ??
                                                    false
                                                ? AppColors.white
                                                : context.themeColors.textColor,
                                      ),
                                    ),
                                  ),
                                  SpaceH(AppSize.w4),
                                  Icon(
                                    Ionicons.caret_down,
                                    // color: isTotalMatched ? context.themeColors.iconColor : AppColors.white,
                                    color: timeBloc.selectedActivityName1 ==
                                            "Activity"
                                        ? context.themeColors.iconColor
                                            .withOpacity(0.7)
                                        : timeSheetList[widget.index].isError ??
                                                false
                                            ? AppColors.white
                                            : context.themeColors.iconColor,
                                    size: AppSize.sp12,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        (timeBloc.selectedActivityId1 == "" ||
                                timeBloc.selectedActivityId1 == null)
                            ? Container(
                                height: AppSize.h10,
                              )
                            : CommonTextField(
                                controller: timeBloc.remarkTextController1,
                                hintText:
                                    "${AppLocalizations.of(context)!.remark}",
                                validate: (timeBloc
                                            .myTimeSheetList
                                            .value[widget.index]
                                            .tb1RemarkRequired ??
                                        false) &&
                                    timeBloc.remarkTextController1.text == "",
                                onChanged: (value) {
                                  timeBloc.myTimeSheetList.value[widget.index]
                                      .tb1Remark = value;
                                  timeBloc.remarkTextController1.text.isEmpty
                                      ? timeBloc.remarkValidate1 = true
                                      : timeBloc.remarkValidate1 = false;
                                  timeBloc.rebuildScreen();
                                },
                              ),
                        // activity 2
                        Padding(
                          padding: EdgeInsets.only(
                            top: AppSize.h10,
                            left: AppSize.w14,
                            right: AppSize.w14,
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    backgroundColor:
                                        context.themeColors.drawerColor,
                                    builder: (BuildContext context) {
                                      return activityTime.TimeSheetTimePicker(
                                        onOkPressed: (String value) {
                                          print(value);
                                          timeSheetList[widget.index].tb2Hours =
                                              value;
                                          timeBloc.checkTotalAndActivityTime();
                                        },
                                        selectedTime:
                                            timeSheetList[widget.index]
                                                    .tb2Hours ??
                                                "00:00",
                                        initialTime: timeSheetList[widget.index]
                                                .tb2Hours ??
                                            "00:00",
                                      );
                                    },
                                  );
                                },
                                child: Text(
                                  timeBloc.myTimeSheetList.value[widget.index]
                                          .tb2Hours ??
                                      "00:00",
                                  style: context.textTheme.bodyMedium?.copyWith(
                                      color: (timeSheetList[widget.index]
                                                  .isError ??
                                              false)
                                          ? AppColors.white
                                          : context.themeColors.textColor,
                                      fontWeight: FontWeight.w500,
                                      fontSize: AppSize.sp14,
                                      letterSpacing: 1),
                                ),
                              ),
                              Spacer(),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      final result = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              ActivitySearchListScreen(
                                            activityIndex: 2,
                                            selectedActivityName:
                                                timeBloc.selectedActivityName2,
                                            selectedActivityId: timeBloc
                                                    .myTimeSheetList
                                                    .value[widget.index]
                                                    .tb2CostCenterId ??
                                                'Activity',
                                          ),
                                        ),
                                      );

                                      if (result != null) {
                                        timeBloc.updateActivityValue2(
                                            result['selectedValue'],
                                            result['selectedActivityId']);
                                      }

                                      timeBloc
                                              .myTimeSheetList
                                              .value[widget.index]
                                              .tb2CostCenterId =
                                          result['selectedActivityId'];

                                      timeBloc.checkRemarkRequiredOrNot(
                                          context: context);
                                    },
                                    child: Text(
                                      timeBloc.selectedActivityName2 ==
                                              "Activity"
                                          ? AppLocalizations.of(context)!
                                              .activityText
                                          : timeBloc.selectedActivityName2,
                                      style: context.textTheme.headlineLarge
                                          ?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.normal,
                                        color: timeBloc.selectedActivityName2 ==
                                                "Activity"
                                            ? context.themeColors.textColor
                                                .withOpacity(0.4)
                                            : (timeBloc
                                                        .myTimeSheetList
                                                        .value[widget.index]
                                                        .isError ??
                                                    false)
                                                ? AppColors.white
                                                : context.themeColors.textColor,
                                      ),
                                    ),
                                  ),
                                  SpaceH(AppSize.w4),
                                  Icon(
                                    Ionicons.caret_down,
                                    // color: isTotalMatched ? context.themeColors.iconColor : AppColors.white,
                                    color: timeBloc.selectedActivityName2 ==
                                            "Activity"
                                        ? context.themeColors.iconColor
                                            .withOpacity(0.7)
                                        : (timeBloc
                                                    .myTimeSheetList
                                                    .value[widget.index]
                                                    .isError ??
                                                false)
                                            ? AppColors.white
                                            : context.themeColors.iconColor,
                                    size: AppSize.sp12,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        (timeBloc.selectedActivityId2 == "" ||
                                timeBloc.selectedActivityId2 == null)
                            ? Container(
                                height: AppSize.h10,
                              )
                            : CommonTextField(
                                controller: timeBloc.remarkTextController2,
                                hintText:
                                    "${AppLocalizations.of(context)!.remark}",
                                validate: (timeBloc
                                            .myTimeSheetList
                                            .value[widget.index]
                                            .tb2RemarkValidate ??
                                        false) &&
                                    timeBloc.remarkTextController2.text == "",
                                onChanged: (value) {
                                  timeBloc.myTimeSheetList.value[widget.index]
                                      .tb2Remark = value;
                                  timeBloc.remarkTextController2.text.isEmpty
                                      ? timeBloc.remarkValidate2 = true
                                      : timeBloc.remarkValidate2 = false;
                                  timeBloc.rebuildScreen();
                                },
                              ),
                        // activity 3
                        Padding(
                          padding: EdgeInsets.only(
                            top: AppSize.h10,
                            left: AppSize.w14,
                            right: AppSize.w14,
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  showModalBottomSheet(
                                    context: context,
                                    backgroundColor:
                                        context.themeColors.drawerColor,
                                    builder: (BuildContext context) {
                                      return activityTime.TimeSheetTimePicker(
                                        onOkPressed: (String value) {
                                          print(value);
                                          timeBloc
                                              .myTimeSheetList
                                              .value[widget.index]
                                              .tb3Hours = value;
                                          timeBloc.checkTotalAndActivityTime();
                                        },
                                        selectedTime:
                                            timeSheetList[widget.index]
                                                    .tb3Hours ??
                                                "00:00",
                                        initialTime: timeSheetList[widget.index]
                                                .tb3Hours ??
                                            "00:00",
                                      );
                                    },
                                  );
                                },
                                child: Text(
                                  timeBloc.myTimeSheetList.value[widget.index]
                                          .tb3Hours ??
                                      "00:00",
                                  style: context.textTheme.bodyMedium?.copyWith(
                                      color: (timeBloc
                                                  .myTimeSheetList
                                                  .value[widget.index]
                                                  .isError ??
                                              false)
                                          ? AppColors.white
                                          : context.themeColors.textColor,
                                      fontWeight: FontWeight.w500,
                                      fontSize: AppSize.sp14,
                                      letterSpacing: 1),
                                ),
                              ),
                              Spacer(),
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      final result = await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              ActivitySearchListScreen(
                                            activityIndex: 3,
                                            selectedActivityName: timeBloc
                                                    .selectedActivityName3 ??
                                                'Activity',
                                            selectedActivityId: timeBloc
                                                    .myTimeSheetList
                                                    .value[widget.index]
                                                    .tb3CostCenterId ??
                                                'Activity',
                                          ),
                                        ),
                                      );
                                      if (result != null) {
                                        timeBloc.updateActivityValue3(
                                            result['selectedValue'],
                                            result['selectedActivityId']);
                                      }
                                      timeBloc
                                              .myTimeSheetList
                                              .value[widget.index]
                                              .tb3CostCenterId =
                                          result['selectedActivityId'];
                                      timeBloc.checkRemarkRequiredOrNot(
                                          context: context);
                                    },
                                    child: Text(
                                      timeBloc.selectedActivityName3 ==
                                              "Activity"
                                          ? AppLocalizations.of(context)!
                                              .activityText
                                          : timeBloc.selectedActivityName3,
                                      style: context.textTheme.headlineLarge
                                          ?.copyWith(
                                        fontSize: AppSize.sp14,
                                        fontWeight: FontWeight.normal,
                                        color: timeBloc.selectedActivityName3 ==
                                                "Activity"
                                            ? context.themeColors.textColor
                                                .withOpacity(0.4)
                                            : (timeBloc
                                                        .myTimeSheetList
                                                        .value[widget.index]
                                                        .isError ??
                                                    false)
                                                ? AppColors.white
                                                : context.themeColors.textColor,
                                      ),
                                    ),
                                  ),
                                  SpaceH(AppSize.w4),
                                  Icon(
                                    Ionicons.caret_down,
                                    // color: isTotalMatched ? context.themeColors.iconColor : AppColors.white,
                                    color: timeBloc.selectedActivityName3 ==
                                            "Activity"
                                        ? context.themeColors.iconColor
                                            .withOpacity(0.7)
                                        : (timeBloc
                                                    .myTimeSheetList
                                                    .value[widget.index]
                                                    .isError ??
                                                false)
                                            ? AppColors.white
                                            : context.themeColors.iconColor,
                                    size: AppSize.sp12,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        (timeBloc.selectedActivityId3 == "" ||
                                timeBloc.selectedActivityId3 == null)
                            ? Container(
                                height: AppSize.h10,
                              )
                            : CommonTextField(
                                controller: timeBloc.remarkTextController3,
                                hintText:
                                    "${AppLocalizations.of(context)!.remark}",
                                validate: (timeBloc
                                            .myTimeSheetList
                                            .value[widget.index]
                                            .tb3RemarkValidate ??
                                        false) &&
                                    timeBloc.remarkTextController3.text == "",
                                onChanged: (value) {
                                  timeBloc.myTimeSheetList.value[widget.index]
                                      .tb3Remark = value;
                                  timeBloc.remarkTextController3.text.isEmpty
                                      ? timeBloc.remarkValidate3 = true
                                      : timeBloc.remarkValidate3 = false;
                                  timeBloc.rebuildScreen();
                                },
                              ),
                        SpaceV(AppSize.h10)
                      ],
                    ),
                  ),
                  Container(
                    height: AppSize.h34,
                    color: context.themeColors.listGridColor1,
                    padding: EdgeInsets.symmetric(horizontal: AppSize.w14),
                    alignment: Alignment.centerLeft,
                    child: Text(AppLocalizations.of(context)!.additional,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.themeColors.textColor,
                          fontSize: AppSize.sp14,
                        )),
                  ),
                  timeBloc.costCentersNonTimeBoundPersonList[0].ntbName1 == null
                      ? Container()
                      : CustomListTile(
                          title: AppLocalizations.of(context)!.lunch,
                          trailingWidget: CustomSwitch(
                            value: timeBloc.myTimeSheetList.value[widget.index]
                                    .ntb1Checked ??
                                false,
                            inactiveThumbColor: AppColors.white,
                            inactiveTrackColor: AppColors.darkModeGreyColor,
                            onChanged: (value) {
                              // appDB.shareAnyltical = !appDB.shareAnyltical;
                              timeBloc.updateLunchValue();
                              //hoursBloc.isLunch = !hoursBloc.isLunch;
                            },
                          )),
                  timeBloc.costCentersNonTimeBoundPersonList[0].ntbName2 == null
                      ? Container()
                      : CustomListTile(
                          title: AppLocalizations.of(context)!.meal,
                          trailingWidget: CustomSwitch(
                            value: timeBloc.myTimeSheetList.value[widget.index]
                                    .ntb2Checked ??
                                false,
                            inactiveThumbColor: AppColors.white,
                            inactiveTrackColor: AppColors.darkModeGreyColor,
                            onChanged: (value) {
                              timeBloc.updateMealValue();
                            },
                          )),
                  timeBloc.costCentersNonTimeBoundPersonList[0].ntbName3 == null
                      ? Container()
                      : CustomListTile(
                          title: AppLocalizations.of(context)!.surChargeService,
                          trailingWidget: CustomSwitch(
                            value: timeBloc.myTimeSheetList.value[widget.index]
                                    .ntb3Checked ??
                                false,
                            inactiveThumbColor: AppColors.white,
                            inactiveTrackColor: AppColors.darkModeGreyColor,
                            onChanged: (value) {
                              timeBloc.updateChange();
                            },
                          )),
                  timeBloc.costCentersNonTimeBoundPersonList[0].ntbName4 == null
                      ? Container()
                      : CustomListTile(
                          title: AppLocalizations.of(context)!.workingFromHome,
                          trailingWidget: CustomSwitch(
                            value: timeBloc.myTimeSheetList.value[widget.index]
                                    .ntb4Checked ??
                                false,
                            inactiveThumbColor: AppColors.white,
                            inactiveTrackColor: AppColors.darkModeGreyColor,
                            onChanged: (value) {
                              timeBloc.updateWorkHome();
                            },
                          )),
                  CommonTextField(
                    controller: timeBloc.remarkTextController,
                    hintText: "${AppLocalizations.of(context)!.remark}",
                    // isRequired: timeBloc.isRemark && timeBloc.remarkTextController.text == "",
                    onChanged: (value) {
                      timeBloc.myTimeSheetList.value[widget.index].remark =
                          value;
                    },
                  ),
                  SpaceV(AppSize.h10),
                  ReusableContainerButton(
                    borderRadius: BorderRadius.zero,
                    backgroundColor: AppColors.primaryColor,
                    onPressed: () {
                      timeBloc.checkRemarkRequiredOrNot(context: context);
                      AppNavigation.previousScreen(context);
                    },
                    buttonText: AppLocalizations.of(context)!.oK.toUpperCase(),
                    height: AppSize.h32,
                    textStyle: TextStyle(
                        fontSize: AppSize.sp14,
                        color: AppColors.white,
                        fontWeight: FontWeight.w500),
                  ),
                  SpaceV(AppSize.h10),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class CommonTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onChanged;
  final bool validate;

  CommonTextField({
    required this.controller,
    required this.hintText,
    required this.onChanged,
    this.validate = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppSize.w4),
      child: TextFormField(
        controller: controller,
        style: TextStyle(fontSize: AppSize.sp14),
        decoration: InputDecoration(
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorText: validate
              ? AppLocalizations.of(context)!.remarkRequiredError
              : null,
          hintText: hintText,
          floatingLabelBehavior: FloatingLabelBehavior.always,
          hintStyle: context.textTheme.bodyMedium?.copyWith(
            fontSize: AppSize.sp14,
            color: context.themeColors.greyColor,
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: AppSize.w10),
        ),
        keyboardType: TextInputType.multiline,
        onChanged: onChanged,
        maxLines: 1,
        cursorColor: context.themeColors.greyColor,
      ),
    );
  }
}
