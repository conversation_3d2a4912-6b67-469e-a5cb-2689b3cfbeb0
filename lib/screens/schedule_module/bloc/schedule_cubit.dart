import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:isoweek/isoweek.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:staff_medewerker/screens/open_service_module/repository/open_service_repository.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_date_picker_cubit.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../../common/custom_widgets/common_snackbar.dart';
import '../../../main.dart';
import '../model/schedule_date_detail_model.dart';
import '../model/schedule_month_model.dart';
import '../repository/schedule_repository.dart';
import '../widget/application_reference_popup.dart';

part 'schedule_state.dart';

class ScheduleCubit extends Cubit<ScheduleState> {
  ScheduleCubit() : super(ScheduleInitial());

  bool isDetail = false;

  ValueNotifier<int> selectDayValue = ValueNotifier(1);
  ValueNotifier<int> selectDayValue1 = ValueNotifier(1);
  String? selectedText;
  TextEditingController swapNoteController = TextEditingController();
  ValueNotifier<bool> toggleMyOpenServiceSubscriptionLoading =
      ValueNotifier(false);

  DateTime? selectedDate;

  String formattedDay = '';
  String? selectedGuid;
  String formattedMonthDate = '';
  String formattedWeek = '';
  bool isDayInList = false;

  List<String?> titleList = [];
  List<String?> applicationReferenceList = [
    'Rooster',
    'Ruilen',
    'Open diensten'
  ];

  List<String?> guids = [];
  DateTime selectDate = DateTime.now();

  ValueNotifier<bool> isScheduleWeekLoading = ValueNotifier(false);
  ValueNotifier<bool> isScheduleMonthLoading = ValueNotifier(true);
  ValueNotifier<bool> isoLoading = ValueNotifier(false);
  ValueNotifier<bool> isDateLoading = ValueNotifier(false);
  ValueNotifier<bool> isCalendarMonthLoading = ValueNotifier(false);
  ValueNotifier<bool> isSetSwapShiftLoading = ValueNotifier(false);
  ValueNotifier<bool> isCancelSwapShiftLoading = ValueNotifier(false);
  ValueNotifier<String> selectedTitles = ValueNotifier('');
  final OpenServiceApiRepository openServiceApi = OpenServiceApiRepository();

  final scheduleApiRepository scheduleApi = scheduleApiRepository();

  List<ScheduleMonthResponseModel> scheduleWeekList = [];
  List<ScheduleMonthResponseModel> scheduleWeekList1 = [];
  List<ScheduleMonthResponseModel> scheduleWeekList2 = [];
  List<ScheduleMonthResponseModel> scheduleMonthList = [];
  List<ScheduleMonthResponseModel> scheduleMonthList1 = [];
  List<ScheduleMonthResponseModel> scheduleFilterWeek = [];
  List<ScheduleDateDetailResponseModel> scheduleDateList = [];
  List<ScheduleDateDetailResponseModel> filterScheduleDateList = [];
  List<ScheduleDateDepartmentListResponseModel> scheduleDepartmentList = [];

  List<DateTime> calendarDayList = [];

  List<ScheduleMonthResponseModel> scheduleDayList = [];
  List<ScheduleMonthResponseModel> scheduleFilter1List = [];
  List<ScheduleMonthResponseModel> scheduleDayList1 = [];

  ValueNotifier<List<Item>> applicationReferenceList1 =
      ValueNotifier<List<Item>>([
    Item('Rooster', true, AppColors.roosterColor),
    Item('Ruilen', false, AppColors.ruilenColor),
    Item('Open diensten', false, AppColors.openDienstenColor),
  ]);

  // Add a notifier to track filter changes for cache invalidation
  ValueNotifier<int> filterChangeNotifier = ValueNotifier<int>(0);

  void updateDate(int value, String direction) {
    final scheduleDayBloc =
        BlocProvider.of<ScheduleTimeCubit>(navigatorKey.currentContext!);

    DateTime newDate;
    if (value == 1) {
      // Day view
      newDate = direction == 'left'
          ? selectDate.subtract(Duration(days: 1))
          : selectDate.add(Duration(days: 1));
    } else if (value == 2) {
      // Week view
      newDate = direction == 'left'
          ? selectDate.subtract(Duration(days: 7))
          : selectDate.add(Duration(days: 7));
    } else if (value == 3) {
      // Month view
      newDate = direction == 'left'
          ? DateTime(selectDate.year, selectDate.month - 1, selectDate.day)
          : DateTime(selectDate.year, selectDate.month + 1, selectDate.day);
    } else {
      return;
    }

    scheduleDayBloc.setDate(selectedValue: newDate);
    selectDate = newDate;
    apiCall(navigatorKey.currentContext!, value);
  }

  List<ScheduleMonthResponseModel> getFilteredScheduleList(
      List<ScheduleMonthResponseModel> fullList,
      DateTime startDate,
      DateTime endDate) {
    return fullList.where((item) {
      log('fullList -------------->$startDate');
      log('fullList -------------->$endDate');
      log('fullList -------------->$fullList');
      log('fullList -------------->${fullList.length}');
      if (item.date != null) {
        final DateTime itemDate = DateTime.parse(item.date!);
        return (itemDate.isAfter(startDate) ||
                itemDate.isAtSameMomentAs(startDate)) &&
            itemDate.isBefore(endDate.add(Duration(days: 1)));
      }
      return false;
    }).toList();
  }

  Future<void> toggleMyOpenServiceSubscriptionApiData({
    required BuildContext context,
    required String personId,
    required String baseServiceId,
    required String departmentId,
    required String date,
    required bool isSubscribe,
  }) async {
    toggleMyOpenServiceSubscriptionLoading.value = true;
    final response = await openServiceApi.toggleMyOpenServiceSubscriptionApi(
      context: context,
      personId: personId,
      baseServiceId: baseServiceId,
      departmentId: departmentId,
      date: date,
    );
    toggleMyOpenServiceSubscriptionLoading.value = false;

    if (response?.done == true && response?.statusCode == 200) {
      isSubscribe
          ? customSnackBar(
              context: navigatorKey.currentContext!,
              message: AppLocalizations.of(navigatorKey.currentContext!)!
                  .subscribeSuccess,
              actionButtonText:
                  AppLocalizations.of(navigatorKey.currentContext!)!
                      .closeText
                      .toUpperCase(),
            )
          : customSnackBar(
              context: navigatorKey.currentContext!,
              message: AppLocalizations.of(navigatorKey.currentContext!)!
                  .unsubscribeSuccess,
              actionButtonText:
                  AppLocalizations.of(navigatorKey.currentContext!)!
                      .closeText
                      .toUpperCase(),
            );
    }
  }

  Future<void> fetchDepartmentAndDayData({bool firstTime = false}) async {
    print("guids  =====>${guids}");
    titleList.clear();

    guids.clear();
    await BlocProvider.of<ScheduleCubit>(navigatorKey.currentContext!)
        .fetchDateDepartmentData(navigatorKey.currentContext!);
    log("titleList  =====>${titleList}");
    log("selectedText =====>${selectedText}");
    log("selectedText =====>${selectedGuid}");

    titleList = scheduleDepartmentList
        .map(
            (item) => item.title) // Replace with the correct method or property
        .toList();
    guids = scheduleDepartmentList.map((item) => item.guid).toList();
    log("guids  =====>1${guids}");
    if (firstTime) {
      selectedText = (selectedText == null)
          ? scheduleDepartmentList[0].title
          : selectedText;

      selectedGuid = (selectedGuid == null) ? guids[0] : selectedGuid;
      emit(ScheduleInitial());

      print("selectedTexts elected Texts  electedText${selectedText}");
      print("selectedTexts elected Texts electedText${selectedGuid}");
      print("selectedTexts elected Texts electedText${selectDayValue.value}");
    }
    // var selectedGuid = guids.isNotEmpty ? guids[0] : '';
    print("guids  =====>3${selectedGuid}");

    await BlocProvider.of<ScheduleCubit>(navigatorKey.currentContext!)
        .fetchDayData(navigatorKey.currentContext!, guid: selectedGuid ?? '');
    //print("guids  =====>2${guids}");
    print("guids  =====>3${selectedGuid}");
    emit(ScheduleInitial());
  }

  void updateDetailValue(bool value) {
    isDetail = value;

    emit(ScheduleInitial());
  }

  void updateSelectedTextValue(String value) {
    selectedText = value;

    emit(ScheduleInitial());
  }

  void updateDayList(bool value) {
    isDayInList = value;

    emit(ScheduleInitial());
  }

  void filterApplicationReferenceListForMonth() {
    if (selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
      scheduleMonthList1 = scheduleMonthList;
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleMonthList1 = scheduleMonthList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap is SwapMonthModel &&
                  (data.swap?.state == 'Aangevraagd' ||
                      data.swap?.state == 'Geaccepteerd' ||
                      data.swap?.state == 'Uitgezet'));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Rooster')) {
      scheduleMonthList1 = scheduleMonthList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return ((data.swap?.state == 'Aangevraagd' ||
              data.swap?.state == 'Uitgezet' ||
              data.swap?.state == 'Geaccepteerd' ||
              (data.swap?.state == null && data.openService == null)));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Rooster') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleMonthList1 = scheduleMonthList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap?.state == null);
        },
      ).toList();
    } else if (selectedTitles.value == 'Open diensten') {
      scheduleMonthList1 = scheduleMonthList
          .where((data) => data.openService is OpenServiceModel)
          .toList();
    } else if (selectedTitles.value == 'Ruilen') {
      scheduleMonthList1 = scheduleMonthList
          .where((data) =>
              data.swap is SwapMonthModel &&
              (data.swap?.state == 'Aangevraagd' ||
                  data.swap?.state == 'Geaccepteerd' ||
                  data.swap?.state == 'Uitgezet'))
          .toList();
    } else if (selectedTitles.value == 'Rooster') {
      scheduleMonthList1 = scheduleMonthList.where((data) {
        print("Swap: ${data.swap}");
        print("State: ${data.swap?.state}");
        return data.swap is SwapMonthModel && (data.swap?.state == null);
      }).toList();
    } else if (selectedTitles.value == '') {
      scheduleMonthList1.clear();
    } else {
      //scheduleMonthList1 = scheduleMonthList;
    }

    // Notify that filters have changed to invalidate calendar cache
    filterChangeNotifier.value++;
    emit(ScheduleInitial());
  }

  void filterApplicationReferenceListForWeek() {
    if (selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
      scheduleWeekList1 = scheduleWeekList;
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleWeekList1 = scheduleWeekList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap is SwapMonthModel &&
                  (data.swap?.state == 'Aangevraagd' ||
                      data.swap?.state == 'Geaccepteerd' ||
                      data.swap?.state == 'Uitgezet'));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Rooster')) {
      scheduleWeekList1 = scheduleWeekList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return ((data.swap?.state == 'Aangevraagd' ||
              data.swap?.state == 'Uitgezet' ||
              data.swap?.state == 'Geaccepteerd' ||
              (data.swap?.state == null && data.openService == null)));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Rooster') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleWeekList1 = scheduleWeekList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap?.state == null);
        },
      ).toList();
    } else if (selectedTitles.value == 'Open diensten') {
      scheduleWeekList1 = scheduleWeekList
          .where((data) => data.openService is OpenServiceModel)
          .toList();
    } else if (selectedTitles.value == 'Ruilen') {
      scheduleWeekList1 = scheduleWeekList
          .where((data) =>
              data.swap is SwapMonthModel &&
              (data.swap?.state == 'Aangevraagd' ||
                  data.swap?.state == 'Geaccepteerd' ||
                  data.swap?.state == 'Uitgezet'))
          .toList();
    } else if (selectedTitles.value == 'Rooster') {
      scheduleWeekList1 = scheduleWeekList.where((data) {
        print("Swap: ${data.swap}");
        print("State: ${data.swap?.state}");
        return data.swap is SwapMonthModel && (data.swap?.state == null);
      }).toList();
    } else if (selectedTitles.value == '') {
      scheduleWeekList1.clear();
    } else {
      //scheduleMonthList1 = scheduleMonthList;
    }

    // Notify that filters have changed to invalidate calendar cache
    filterChangeNotifier.value++;
    emit(ScheduleInitial());
  }

  // void filterApplicationReferenceListForWeek() {
  //   DateTime getDate(DateTime d) => DateTime(d.year, d.month, d.day);
  //   DateTime firstDayOfWeek = getDate(selectDate.subtract(Duration(days: selectDate.weekday - 1)));
  //   DateTime lastDayOfWeek = getDate(selectDate.add(Duration(days: DateTime.daysPerWeek - selectDate.weekday)));
  //   log('Start of week: ${getDate(selectDate.subtract(Duration(days: selectDate.weekday - 1)))}');
  //   log('End of week: ${getDate(selectDate.add(Duration(days: DateTime.daysPerWeek - selectDate.weekday)))}');
  //   scheduleFilter1List = getFilteredScheduleList(scheduleFilter1List, firstDayOfWeek, lastDayOfWeek);
  //   if (selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
  //     scheduleFilterWeek = scheduleFilter1List;
  //   } else if (selectedTitles.value.contains('Ruilen') && selectedTitles.value.contains('Open diensten')) {
  //     scheduleFilterWeek = scheduleFilter1List.where(
  //       (data) {
  //         print("State: ${data.swap?.state}");
  //         return data.openService is OpenServiceModel ||
  //             (data.swap is SwapMonthModel && (data.swap?.state == 'Aangevraagd' || data.swap?.state == 'Uitgezet'));
  //       },
  //     ).toList();
  //   } else if (selectedTitles.value.contains('Ruilen') && selectedTitles.value.contains('Rooster')) {
  //     scheduleFilterWeek = scheduleFilter1List.where(
  //       (data) {
  //         print("State: ${data.swap?.state}");
  //         return ((data.swap?.state == 'Aangevraagd' || data.swap?.state == 'Uitgezet' || data.swap?.state == null));
  //       },
  //     ).toList();
  //   } else if (selectedTitles.value.contains('Rooster') && selectedTitles.value.contains('Open diensten')) {
  //     scheduleFilterWeek = scheduleFilter1List.where(
  //       (data) {
  //         print("State: ${data.swap?.state}");
  //         return data.openService is OpenServiceModel || (data.swap?.state == null);
  //       },
  //     ).toList();
  //   } else if (selectedTitles.value == 'Open diensten') {
  //     scheduleFilterWeek = scheduleFilter1List.where((data) => data.openService is OpenServiceModel).toList();
  //   } else if (selectedTitles.value == 'Ruilen') {
  //     scheduleFilterWeek = scheduleFilter1List
  //         .where((data) =>
  //             data.swap is SwapMonthModel && (data.swap?.state == 'Aangevraagd' || data.swap?.state == 'Uitgezet'))
  //         .toList();
  //   } else if (selectedTitles.value == 'Rooster') {
  //     scheduleFilterWeek = scheduleFilter1List.where((data) {
  //       print("Swap: ${data.swap}");
  //       print("State: ${data.swap?.state}");
  //       return data.swap is SwapMonthModel && (data.swap?.state == null);
  //     }).toList();
  //   } else {
  //     //scheduleMonthList1 = scheduleMonthList;
  //   }
  //   print("scheduleWeekList1: ${scheduleFilterWeek}");
  //   print("scheduleWeekList1: ${scheduleFilter1List}");
  //   emit(ScheduleInitial());
  // }

  void filterApplicationReferenceListForCalendar() {
    if (selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
      scheduleDayList1 = scheduleDayList;
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleDayList1 = scheduleDayList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap is SwapMonthModel &&
                  (data.swap?.state == 'Aangevraagd' ||
                      data.swap?.state == 'Geaccepteerd' ||
                      data.swap?.state == 'Uitgezet'));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Ruilen') &&
        selectedTitles.value.contains('Rooster')) {
      scheduleDayList1 = scheduleDayList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return ((data.swap?.state == 'Aangevraagd' ||
              data.swap?.state == 'Geaccepteerd' ||
              data.swap?.state == 'Uitgezet' ||
              (data.swap?.state == null && data.openService == null)));
        },
      ).toList();
    } else if (selectedTitles.value.contains('Rooster') &&
        selectedTitles.value.contains('Open diensten')) {
      scheduleDayList1 = scheduleDayList.where(
        (data) {
          print("State: ${data.swap?.state}");
          return data.openService is OpenServiceModel ||
              (data.swap?.state == null);
        },
      ).toList();
    } else if (selectedTitles.value == 'Open diensten') {
      scheduleDayList1 = scheduleDayList
          .where((data) => data.openService is OpenServiceModel)
          .toList();
    } else if (selectedTitles.value == 'Ruilen') {
      scheduleDayList1 = scheduleDayList
          .where((data) =>
              data.swap is SwapMonthModel &&
              (data.swap?.state == 'Aangevraagd' ||
                  data.swap?.state == 'Geaccepteerd' ||
                  data.swap?.state == 'Uitgezet'))
          .toList();
    } else if (selectedTitles.value == 'Rooster') {
      scheduleDayList1 = scheduleDayList.where((data) {
        print("Swap: ${data.swap}");
        print("State: ${data.swap?.state}");
        return data.swap is SwapMonthModel && (data.swap?.state == null);
      }).toList();
    } else {
      //scheduleMonthList1 = scheduleMonthList;
    }
    print("scheduleDayList1: ${scheduleDayList1}");
    print("scheduleDayList1: ${scheduleDayList}");

    // Notify that filters have changed to invalidate calendar cache
    filterChangeNotifier.value++;
    emit(ScheduleInitial());
  }

  void updateScheduleDayList(List<ScheduleDateDetailResponseModel> value) {
    filterScheduleDateList = value;

    emit(ScheduleInitial());
  }

  // bool isScheduleWeekDataFirstTimeApiCall = true;
  // bool isScheduleMonthDataFirstTimeApiCall = true;

  Future<void> scheduleWeekData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    // if (isScheduleWeekDataFirstTimeApiCall) {
    //   isScheduleWeekLoading.value = true;
    // }
    isScheduleWeekLoading.value = true;

    final response = await scheduleApi.scheduleWeekApi(
        context: context, iosYearWeek: iosYearWeek);
    // if (isScheduleWeekDataFirstTimeApiCall == false) {
    //   isScheduleWeekLoading.value = true;
    //   Future.delayed(
    //     Duration(milliseconds: 90),
    //     () {
    //       isScheduleWeekLoading.value = false;
    //     },
    //   );
    // }
    log("scheduleFilterWeek api done =====>${iosYearWeek}");

    // scheduleWeekList.clear();
    // scheduleWeekList1 = response!;
    // scheduleFilterWeek = response;
    //
    // scheduleWeekList = response.map((item) => new ScheduleMonthResponseModel.clone(item)).toList();

    if (response != null) {
      scheduleWeekList.clear();
      scheduleWeekList1.clear();
      scheduleWeekList1 = response;
      // scheduleMonthList1 = scheduleMonthList;
      scheduleWeekList = response
          .map((item) => new ScheduleMonthResponseModel.clone(item))
          .toList();
      // scheduleDayList = response.map((item) => new ScheduleMonthResponseModel.clone(item)).toList();
      //scheduleFilter1List = response.map((item) => new ScheduleMonthResponseModel.clone(item)).toList();
      log("scheduleFilterWeek api done =====>${response}");
      log("scheduleFilterWeek api done =====>${response.length}");
      log("scheduleFilterWeek api done =====>${scheduleWeekList1.length}");
    }
    isScheduleWeekLoading.value = false;

    // if (isScheduleWeekDataFirstTimeApiCall) {
    //   isScheduleWeekLoading.value = false;
    //   isScheduleWeekDataFirstTimeApiCall = false;
    // }

    // if(response?.statusCode != 200){
    //   customSnackBar(
    //     context: navigatorKey.currentContext!,
    //     message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
    //     actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
    //   );
    // }
  }

  Future<void> scheduleMonthData({
    required BuildContext context,
    required String iosYearMonth,
  }) async {
    // if (isScheduleMonthDataFirstTimeApiCall) {
    //   isScheduleMonthLoading.value = true;
    // }
    isScheduleMonthLoading.value = true;

    final response = await scheduleApi.scheduleMonthApi(
        context: context, iosYearMonth: iosYearMonth);
    if (response != null) {
      scheduleMonthList.clear();
      scheduleMonthList1.clear();
      scheduleMonthList1 = response;
      scheduleDayList1 = response;
      // scheduleMonthList1 = scheduleMonthList;
      scheduleMonthList = response
          .map((item) => new ScheduleMonthResponseModel.clone(item))
          .toList();
      scheduleDayList = response
          .map((item) => new ScheduleMonthResponseModel.clone(item))
          .toList();
      scheduleFilter1List = response
          .map((item) => new ScheduleMonthResponseModel.clone(item))
          .toList();
    }
    // print("========================>${scheduleMonthList1}");
    // print("========================>${scheduleMonthList}");
    //
    // print('---------------->${scheduleMonthList.where((data) => data.swap is SwapMonthModel).toList()}');
    // print("selectedTitles.value =====>${selectedTitles.value}");
    // print("month schedule api done =====>${selectedTitles.value == ['Rooster', 'Ruilen', 'Open diensten']}");

    isScheduleMonthLoading.value = false;

    emit(ScheduleInitial());
  }

  Future<void> scheduleDateDepartmentApi({
    required BuildContext context,
    required String iSODate,
  }) async {
    isoLoading.value = true;
    final response = await scheduleApi.scheduleDateDepartmentApi(
        context: context, iSODate: iSODate);
    isoLoading.value = false;
    if (response != null) {
      scheduleDepartmentList.clear();
      scheduleDepartmentList = response;
    }
  }

  Future<void> scheduleDateData({
    required BuildContext context,
    required String iosDate,
    required String guid,
  }) async {
    try {
      isDateLoading.value = true;
      final response = await scheduleApi.scheduleDateApi(
          context: context, iosDate: iosDate, guid: guid);
      if (response != null) {
        scheduleDateList.clear();
        scheduleDateList = response;
      }
      //print("schedule api done =====>${response}");

      isDateLoading.value = false;
    } catch (e) {
      isDateLoading.value = false;
    }
    // if(response?.statusCode != 200){
    //   customSnackBar(
    //     context: navigatorKey.currentContext!,
    //     message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
    //     actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
    //   );
    // }
  }

  Future<void> fetchMonthData(BuildContext context,
      {bool isApiCall = true}) async {
    String formattedDate = DateFormat('yyyyMM').format(selectDate);
    print("formatted date ======>${formattedDate}");
    await scheduleMonthData(
      context: context,
      iosYearMonth: formattedDate,
    );
    // await scheduleMonthData(
    //   context: context,
    //   iosYearMonth: '202402',
    // );
    filterApplicationReferenceListForMonth();
    emit(ScheduleInitial());
  }

  Future<void> fetchCalendarData(BuildContext context) async {
    String formattedDate = DateFormat('yyyyMM').format(selectDate);
    print("formatted date ======>${formattedDate}");
    await scheduleMonthData(context: context, iosYearMonth: formattedDate);

    emit(ScheduleInitial());
  }

  Future<void> fetchWeekData(BuildContext context) async {
    final selectedYear = DateFormat('yyyy').format(selectDate);
    // print("formatted week ======>${format}");
    // fetchMonthData(context)
    //   ..then(
    //     (value) {
    //       int weekNumber = (DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays / 7).ceil();
    //       DateTime getDate(DateTime d) => DateTime(d.year, d.month, d.day);
    //       DateTime firstDayOfWeek = getDate(selectDate.subtract(Duration(days: selectDate.weekday - 1)));
    //       DateTime lastDayOfWeek = getDate(selectDate.add(Duration(days: DateTime.daysPerWeek - selectDate.weekday)));
    //       log('Start of week: ${getDate(selectDate.subtract(Duration(days: selectDate.weekday - 1)))}');
    //       log('End of week: ${getDate(selectDate.add(Duration(days: DateTime.daysPerWeek - selectDate.weekday)))}');
    //       scheduleFilterWeek = getFilteredScheduleList(scheduleMonthList, firstDayOfWeek, lastDayOfWeek);
    //       log('scheduleFilterWeek End of week: ${scheduleFilterWeek}');
    //       log('scheduleFilterWeek End of week: ${scheduleMonthList.length}');
    //     },
    //   );
    //int weekNumber = (selectDate.difference(DateTime(selectDate.year, 1, 1)).inDays ~/ 7) + 1;
    //print("formatted week ======>${format + weekNumber.toString()}");
    print("selectDateselectDateselectDateselectDate${selectDate}");
    print(
        "selectDateselectDateselectDateselectDate${Week.fromDate(selectDate).weekNumber}");
    String weekNumber =
        Week.fromDate(selectDate).weekNumber.toString().padLeft(2, "0");
    print("formatted week ======> ${selectedYear}$weekNumber");

    await scheduleWeekData(
        context: context, iosYearWeek: '${selectedYear}$weekNumber');

    print("formatted week ======>${scheduleFilterWeek}");
    filterApplicationReferenceListForWeek();

    emit(ScheduleInitial());
  }

  Future<void> fetchDateDepartmentData(BuildContext context) async {
    await scheduleDateDepartmentApi(
      context: context,
      iSODate: DateFormat('yyyyMMdd').format(selectDate),
    );
    //scheduleDateDepartmentApi(context: context, iSODate: '20231020',);
    print(
        "fetchDateDepartmentData ======>${DateFormat('yyyyMMdd').format(selectDate)}");
    emit(ScheduleInitial());
  }

  Future<void> fetchDayData(BuildContext context,
      {required String guid}) async {
    await scheduleDateData(
      context: context,
      iosDate: DateFormat('yyyyMMdd').format(selectDate),
      guid: guid,
    );
    emit(ScheduleInitial());
  }

  Future<void> apiCall(BuildContext context, int selectValue) async {
    if (selectValue == 1) {
      // guids = scheduleDepartmentList.map((item) => item.guid).toList();
      // BlocProvider.of<ScheduleCubit>(context).fetchDayData(context, guid: guids[selectDayValue.value - 1] ?? '');
      log('selectedGuid apiCall =============>$selectedGuid');

      BlocProvider.of<ScheduleCubit>(context)
          .fetchDepartmentAndDayData(firstTime: true);
      log('selectedGuid =============>$selectedGuid');
      // selectedText = AppLocalizations.of(context)!.noDepartment;
      //selectDayValue.value = 1;
      //   log("view change =====>${guids[selectDayValue.value - 1]}");
    } else if (selectValue == 2) {
      BlocProvider.of<ScheduleCubit>(context).fetchWeekData(context);
    } else if (selectValue == 3) {
      BlocProvider.of<ScheduleCubit>(context).fetchMonthData(context);
    } else if (selectValue == 4) {
      await BlocProvider.of<ScheduleCubit>(context).fetchCalendarData(context);
      // if (scheduleDayList.isNotEmpty) {
      //   scheduleDayList.addAll(scheduleDayList);
      // }

      log("day list ========>${scheduleDayList.toList()}");
      log("day list ========>${selectDate}");
    }
    emit(ScheduleInitial());
  }

  Future<void> scheduleCalendarMonthData({
    required BuildContext context,
  }) async {
    isCalendarMonthLoading.value = true;
    String formattedDate = DateFormat('yyyyMM').format(selectDate);
    final response = await scheduleApi.scheduleCalendarMonthApi(
        context: context, iosYearMonth: formattedDate);

    print("schedule calendar api done =====>${response}");
    if (response != null) {
      String? path;
      if (Platform.isAndroid) {
        await getTemporaryDirectory().then((value) => path = value.path);
      } else if (Platform.isIOS) {
        await getTemporaryDirectory().then((value) => path = value.path);
      }
      Future<String> createICSFile() async {
        String icsContent = response.data;
        final filePath = '${path}/event.ics';
        File file = File(filePath);
        await file.writeAsString(icsContent);
        return filePath;
      }

      String filePath = await createICSFile();

      OpenFile.open(filePath, type: 'text/calendar');
    }
    isCalendarMonthLoading.value = false;
  }

  Future<void> setSwapAndServiceApiData(
      {required BuildContext context,
      required String personId,
      required String dateEntryId,
      required String stateId,
      required String nextStateId,
      required String remark}) async {
    isSetSwapShiftLoading.value = true;
    final response = await scheduleApi.setSwapAndServiceApi(
      context: context,
      personId: personId,
      dateEntryId: dateEntryId,
      nextStateId: nextStateId,
      stateId: stateId,
      remark: remark,
    );
    isSetSwapShiftLoading.value = false;

    if (response != null &&
        response.statusCode == 200 &&
        response.data['Done'] == true) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message:
            AppLocalizations.of(navigatorKey.currentContext!)!.requestToSwap,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }

  Future<void> cancelSwapAndServiceApiData({
    required BuildContext context,
    required String personId,
    required String dateEntryId,
    required String stateId,
    required String nextStateId,
  }) async {
    isCancelSwapShiftLoading.value = true;
    final response = await scheduleApi.cancelSwapAndServiceApi(
      context: context,
      personId: personId,
      dateEntryId: dateEntryId,
      nextStateId: nextStateId,
      stateId: stateId,
    );
    isCancelSwapShiftLoading.value = false;

    if (response != null &&
        response.statusCode == 200 &&
        response.data['Done'] == true) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message:
            AppLocalizations.of(navigatorKey.currentContext!)!.swapWithdrawn,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
            .closeText
            .toUpperCase(),
      );
    }
  }
}
