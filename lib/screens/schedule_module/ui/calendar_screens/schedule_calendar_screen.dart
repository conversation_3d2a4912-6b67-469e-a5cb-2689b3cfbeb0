import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../../../../utils/colors/app_colors.dart';
import '../../bloc/schedule_date_picker_cubit.dart';
import '../../model/schedule_month_model.dart';
import '../../widget/application_reference_popup.dart';
import '../month_screens/schedule_month_time_detail_screen.dart';

class ScheduleCalenderScreen extends StatefulWidget {
  ScheduleCalenderScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleCalenderScreen> createState() => _ScheduleCalenderScreenState();
}

class _ScheduleCalenderScreenState extends State<ScheduleCalenderScreen> {
  List dateMonth = [];
  Map<String, List<ScheduleMonthResponseModel>> dateToData =
      {}; // Changed to String key for month-based caching
  List<ScheduleMonthResponseModel> selectedData = [];
  bool isSelectedData1 = false;
  bool isLoadingData = false; // Add loading state
  CalendarController _controller = CalendarController();

  // Helper method to generate month key for caching
  String _getMonthKey(DateTime date) {
    return DateFormat('yyyyMM').format(date);
  }

  // Helper method to apply filters to schedule data
  void _applyFiltersToScheduleData(
      ScheduleCubit scheduleBloc, List<ScheduleMonthResponseModel> data) {
    if (scheduleBloc.selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
      scheduleBloc.scheduleDayList1 = data;
    } else if (scheduleBloc.selectedTitles.value.contains('Ruilen') &&
        scheduleBloc.selectedTitles.value.contains('Open diensten')) {
      scheduleBloc.scheduleDayList1 = data.where((item) {
        return item.openService is OpenServiceModel ||
            (item.swap is SwapMonthModel &&
                (item.swap?.state == 'Aangevraagd' ||
                    item.swap?.state == 'Geaccepteerd' ||
                    item.swap?.state == 'Uitgezet'));
      }).toList();
    } else if (scheduleBloc.selectedTitles.value.contains('Ruilen') &&
        scheduleBloc.selectedTitles.value.contains('Rooster')) {
      scheduleBloc.scheduleDayList1 = data.where((item) {
        return (item.swap?.state == 'Aangevraagd' ||
            item.swap?.state == 'Uitgezet' ||
            item.swap?.state == null);
      }).toList();
    } else if (scheduleBloc.selectedTitles.value.contains('Rooster') &&
        scheduleBloc.selectedTitles.value.contains('Open diensten')) {
      scheduleBloc.scheduleDayList1 = data.where((item) {
        return item.openService is OpenServiceModel ||
            (item.swap?.state == null);
      }).toList();
    } else if (scheduleBloc.selectedTitles.value == 'Open diensten') {
      scheduleBloc.scheduleDayList1 =
          data.where((item) => item.openService is OpenServiceModel).toList();
    } else if (scheduleBloc.selectedTitles.value == 'Ruilen') {
      scheduleBloc.scheduleDayList1 = data
          .where((item) =>
              item.swap is SwapMonthModel &&
              (item.swap?.state == 'Aangevraagd' ||
                  item.swap?.state == 'Uitgezet'))
          .toList();
    } else if (scheduleBloc.selectedTitles.value == 'Rooster') {
      scheduleBloc.scheduleDayList1 = data.where((item) {
        return item.swap is SwapMonthModel && (item.swap?.state == null);
      }).toList();
    } else {
      scheduleBloc.scheduleDayList1 = data;
    }
  }

  // Method to clear cache when filters change
  void _clearCache() {
    dateToData.clear();
    log("Cache cleared due to filter changes");
  }

  @override
  void initState() {
    super.initState();
    // Listen for filter changes and clear cache when they occur
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final scheduleBloc = context.read<ScheduleCubit>();
      scheduleBloc.filterChangeNotifier.addListener(() {
        log("Filter changed, clearing calendar cache");
        _clearCache();
        if (mounted) {
          setState(() {});
        }
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    void showDetailedItem(ScheduleMonthResponseModel item) {
      AppNavigation.nextScreen(
        context,
        ScheduleMonthTimeDetailScreen(scheduleMonthData: item),
      );
    }

    print('aaa------------>${MediaQuery.of(context).size.height * 0.75}');
    return BlocBuilder<ScheduleCubit, ScheduleState>(
      builder: (ctx, state) {
        final scheduleBloc = ctx.read<ScheduleCubit>();
        _controller.displayDate = scheduleBloc.selectDate;
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 0,
          ),
          child: FittedBox(
            child: Container(
              color: context.themeColors.homeContainerColor,
              //color: Colors.red,
              height: MediaQuery.of(context).size.height * 0.75,

              width: MediaQuery.of(context).size.width,
              child: MediaQuery(
                data: MediaQuery.of(context).removePadding(removeTop: true),
                child: SfCalendar(
                  controller: _controller,
                  view: CalendarView.month,
                  firstDayOfWeek: DateTime.monday,
                  cellBorderColor: AppColors.transparent,
                  headerHeight:
                      7, // Set the header height to 0 to remove top padding

                  viewHeaderStyle: ViewHeaderStyle(
                    backgroundColor: Colors.transparent,
                    dayTextStyle: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                  todayTextStyle: context.textTheme.bodyMedium?.copyWith(
                    color: context.themeColors.textColor,
                    fontSize: AppSize.sp13,
                  ),
                  headerStyle: CalendarHeaderStyle(
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.transparent,
                    fontSize: AppSize.sp13,
                  )),
                  todayHighlightColor: AppColors.transparent,
                  selectionDecoration: BoxDecoration(
                    color: AppColors.transparent,
                    shape: BoxShape.rectangle,
                  ),
                  dataSource: _getCalendarDataSource(),

                  onSelectionChanged:
                      (CalendarSelectionDetails calendarSelectionDetails) {
                    print(
                        'onSelectionChanged 1----------->${_controller.displayDate}');
                    print(
                        'onSelectionChanged 1----------->${_controller.displayDate}');
                    print(
                        'onSelectionChanged 1----------->${calendarSelectionDetails.date}');
                    print(
                        'onSelectionChanged 1----------->${calendarSelectionDetails.date?.day}');
                    setState(() {
                      scheduleBloc.selectDate = calendarSelectionDetails.date ??
                          DateTime.now(); // Update selectedDate
                    });
                    DateFormat formatter = DateFormat('yyyy-MM-dd');
                    String formattedSelectedDay =
                        formatter.format(scheduleBloc.selectDate);
                    scheduleBloc.isDayInList =
                        scheduleBloc.scheduleDayList1.any((date) {
                      String formattedDay =
                          formatter.format(DateTime.parse(date.date ?? ''));
                      log('formattedDay ------------->${formattedDay}');

                      return formattedDay == formattedSelectedDay;
                    });
                    scheduleBloc.updateDayList(scheduleBloc.isDayInList);
                    if (scheduleBloc.isDayInList) {
                      List<ScheduleMonthResponseModel> matchingItems = [];
                      log('------------->${selectedData}');

                      if (isSelectedData1 == true) {
                        for (var scheduleItem in selectedData) {
                          String formattedItemDay = formatter
                              .format(DateTime.parse(scheduleItem.date ?? ''));
                          if (formattedItemDay == formattedSelectedDay) {
                            matchingItems.add(scheduleItem);
                          }
                        }
                      } else {
                        for (var scheduleItem
                            in scheduleBloc.scheduleDayList1) {
                          String formattedItemDay = formatter
                              .format(DateTime.parse(scheduleItem.date ?? ''));
                          if (formattedItemDay == formattedSelectedDay) {
                            matchingItems.add(scheduleItem);
                          }
                        }
                      }
                      if (matchingItems.isNotEmpty) {
                        if (matchingItems.length > 1) {
                          print('showModalBottomSheet----------->');
                          showModalBottomSheet(
                            context: context,
                            builder: (context) {
                              return Container(
                                height: AppSize.h90,
                                child: ListView.builder(
                                  itemCount: matchingItems.length,
                                  itemBuilder: (context, index) {
                                    return ListTile(
                                      title: Text(
                                        '${matchingItems[index].timeFrom.toString()}-${matchingItems[index].timeUntil.toString()} ${matchingItems[index].department.toString()}',
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: context.themeColors.textColor,
                                          fontSize: AppSize.sp14,
                                        ),
                                      ),
                                      // trailing: (matchingItems[index].swap?.state != null)
                                      //     ? Icon(
                                      //         Ionicons.swap_horizontal_outline,
                                      //         size: AppSize.sp20,
                                      //         color: AppColors.primaryColor,
                                      //       )
                                      //     : (matchingItems[index].openService != null)
                                      //         ? Icon(
                                      //             Ionicons.create_outline,
                                      //             size: AppSize.sp20,
                                      //             color: AppColors.primaryColor,
                                      //           )
                                      //         : Container(),
                                      onTap: () {
                                        Navigator.pop(
                                            context); // Close the bottom sheet
                                        showDetailedItem(matchingItems[index]);
                                      },
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        } else {
                          showDetailedItem(matchingItems[0]);
                        }
                      }
                    } else {
                      print('1----------->');
                    }
                  },
                  cellEndPadding: 5,
                  onViewChanged: (viewChangedDetails) {
                    SchedulerBinding.instance
                        .addPostFrameCallback((duration) async {
                      try {
                        // Prevent multiple simultaneous API calls
                        if (isLoadingData) return;

                        scheduleBloc.selectDate =
                            viewChangedDetails.visibleDates.first;
                        ctx
                            .read<ScheduleTimeCubit>()
                            .setDate(selectedValue: scheduleBloc.selectDate);

                        String monthKey = _getMonthKey(scheduleBloc.selectDate);
                        log("Checking data for month: $monthKey");

                        if (!dateToData.containsKey(monthKey)) {
                          log("Month $monthKey not in cache, fetching data.");
                          isSelectedData1 = false;
                          isLoadingData = true;

                          try {
                            await BlocProvider.of<ScheduleCubit>(context)
                                .fetchCalendarData(context);

                            if (scheduleBloc.scheduleDayList.isNotEmpty) {
                              scheduleBloc
                                  .filterApplicationReferenceListForCalendar();
                              List<ScheduleMonthResponseModel> copy =
                                  List.of(scheduleBloc.scheduleDayList);
                              dateToData[monthKey] = copy;
                              log("Data saved in cache for month: $monthKey");

                              // Apply filters to the fresh data
                              _applyFiltersToScheduleData(scheduleBloc, copy);
                            } else {
                              log("No data received for month: $monthKey");
                              scheduleBloc.scheduleDayList1 = [];
                            }
                          } catch (e) {
                            log("Error fetching calendar data: $e");
                            scheduleBloc.scheduleDayList1 = [];
                          } finally {
                            isLoadingData = false;
                          }
                        } else {
                          log("Using cached data for month: $monthKey");
                          isSelectedData1 = true;
                          selectedData = dateToData[monthKey]!;

                          if (selectedData.isNotEmpty) {
                            // Apply filters to cached data
                            _applyFiltersToScheduleData(
                                scheduleBloc, selectedData);
                            log("Applied filters to cached data for month: $monthKey");
                          } else {
                            log("Cached data is empty for month: $monthKey");
                            scheduleBloc.scheduleDayList1 = [];
                          }
                        }

                        // Ensure UI updates
                        if (mounted) {
                          setState(() {});
                        }
                      } catch (e) {
                        log("Error in onViewChanged: $e");
                        isLoadingData = false;
                        if (mounted) {
                          setState(() {});
                        }
                      }
                    });
                  },

                  appointmentTextStyle: TextStyle(
                      color: navigatorKey
                          .currentContext!.themeColors.homeContainerColor,
                      fontSize: AppSize.sp12,
                      height:
                          1, // Adjust height to reduce vertical space for appointment text

                      fontWeight: FontWeight.w500),

                  monthViewSettings: MonthViewSettings(
                    appointmentDisplayCount: 4,
                    appointmentDisplayMode:
                        MonthAppointmentDisplayMode.appointment,
                    monthCellStyle: MonthCellStyle(
                      todayBackgroundColor:
                          context.themeColors.calendarTodayDateColor,
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp14,
                      ),
                    ),
                    showTrailingAndLeadingDates: false,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

DataSource _getCalendarDataSource() {
  final scheduleBloc =
      BlocProvider.of<ScheduleCubit>(navigatorKey.currentContext!);
  final List<Appointment> appointments = <Appointment>[];
  List<String?> temp = [];
  Map<String, int> dateCount = {}; // Map to store date counts

  // Helper function to determine the color based on the schedule item type
  Color _getItemColor(element, BuildContext context) {
    final scheduleBloc = context.read<ScheduleCubit>();
    final applicationReferenceList =
        scheduleBloc.applicationReferenceList1.value;

    // Check if it's an Open diensten (Open Service)
    if (element.openService != null) {
      final openDienstenItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Open diensten',
        orElse: () => Item('Open diensten', false, AppColors.openDienstenColor),
      );
      return openDienstenItem.color;
    }
    // Check if it's a Ruilen (Swap) with specific states
    else if (element.swap != null &&
        (element.swap?.state == 'Aangevraagd' ||
            element.swap?.state == 'Geaccepteerd' ||
            element.swap?.state == 'Uitgezet')) {
      final ruilenItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Ruilen',
        orElse: () => Item('Ruilen', false, AppColors.ruilenColor),
      );
      return ruilenItem.color;
    }
    // Default to Rooster (Regular schedule)
    else {
      final roosterItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Rooster',
        orElse: () => Item('Rooster', false, AppColors.roosterColor),
      );
      return roosterItem.color;
    }
  }

  scheduleBloc.scheduleDayList1.forEach((element) {
    // log(element.toJson().toString(), name: 'full Model');
    // log('=================>>>', name: 'full Model');
    if (element.date != null) {
      // log((element.swap!=null?element.swap!.toJson().toString() : 'no swap'), name: 'full Model swap: ');

      if (!temp.contains(element.date)) {
        print("------->${element.date}");
        // print("------->${element.timeFrom}");
        // print("------->${element.timeUntil}");

        int count = dateCount[element.date] ?? 0; // Get the count for the date
        Color itemColor = _getItemColor(
            element, navigatorKey.currentContext!); // Get the appropriate color

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.costCenters ?? 's',
          color: itemColor,
        ));

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.timeFrom ?? '',
          color: itemColor,
        ));

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: element.timeUntil ?? '',
          color: itemColor,
        ));

        temp.add(element.date);
        dateCount[element.date ?? ''] = count + 1;
      } else {
        int count = dateCount[element.date] ?? 0;

        appointments.add(Appointment(
          startTime: DateTime.parse(element.date!),
          endTime: DateTime.parse(element.date!),
          subject: '+${count}',
          color: AppColors.primaryColor,
        ));
      }
    }
  });

  return DataSource(appointments);
}

class DataSource extends CalendarDataSource {
  DataSource(List<Appointment> source) {
    appointments = source;
  }
}
