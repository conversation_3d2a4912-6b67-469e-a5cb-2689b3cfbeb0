import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/authentication_module/widget/common_button.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../../common/custom_widgets/appbar_custom.dart';
import '../../../../common/custom_widgets/spacebox.dart';
import '../../../../utils/appsize.dart';
import '../../../profile_module/widget/common/common_info_row.dart';
import '../../bloc/schedule_cubit.dart';
import '../../model/schedule_month_model.dart';

class ScheduleMonthTimeDetailScreen extends StatelessWidget {
  final ScheduleMonthResponseModel? scheduleMonthData;

  const ScheduleMonthTimeDetailScreen({Key? key, this.scheduleMonthData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // print("=====>${scheduleMonthData?.swap?.remark}");
    // String message = RegExp(r"Collega:\s*(.+)").firstMatch(scheduleMonthData?.swap?.remark ?? '')?.group(1) ?? '';
    //  print("firstMatch ============>${scheduleMonthData?.openService}");

    return Scaffold(
      appBar: CustomAppBar(
          title: DateFormat('EEEE d MMMM', appDB.language).format(
              DateTime.parse(scheduleMonthData?.iSODate.toString() ?? ''))),
      backgroundColor: context.themeColors.homeContainerColor,
      resizeToAvoidBottomInset: true,
      body: Padding(
        padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
        child: SingleChildScrollView(
          child: BlocBuilder<ScheduleCubit, ScheduleState>(
            builder: (ctx, state) {
              final scheduleBloc = ctx.read<ScheduleCubit>();

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SpaceV(AppSize.h10),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.startScheduleTime,
                    value: scheduleMonthData?.timeFrom ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.endTime,
                    value: scheduleMonthData?.timeUntil ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.department,
                    value: scheduleMonthData?.department ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.breakScheduleText,
                    value: scheduleMonthData?.breakTime ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.costCenter,
                    value: scheduleMonthData?.costCenters ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.service,
                    value: scheduleMonthData?.service ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.assignment,
                    value: (scheduleMonthData?.calendarEntry != null &&
                            scheduleMonthData?.calendarEntry != '')
                        ? '${scheduleMonthData?.calendarEntry?.id ?? ''}\n${scheduleMonthData?.calendarEntry?.relation ?? ''}\n${scheduleMonthData?.calendarEntry?.title ?? ''}' ??
                            '-'
                        : '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.remark,
                    value: scheduleMonthData?.remark ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  CommonInfoRow(
                    title: AppLocalizations.of(context)!.dayRemark,
                    value: scheduleMonthData?.dayRemark ?? '-',
                    titleValue: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp13,
                        color: context.themeColors.darkGreyColor),
                    valueStyle: context.textTheme.titleMedium?.copyWith(
                        fontSize: AppSize.sp12,
                        color: context.themeColors.darkGreyColor),
                    titlePadding: 0,
                  ),
                  if (scheduleMonthData?.swap?.remark?.isNotEmpty == true &&
                      scheduleMonthData?.swap?.remark != null) ...{
                    CommonInfoRow(
                      title: AppLocalizations.of(context)!.remarkFromCollege,
                      value: RegExp(r"Collega:\s*(.+)")
                              .firstMatch(scheduleMonthData?.swap?.remark ?? '')
                              ?.group(1) ??
                          '-',
                      titleValue: context.textTheme.titleMedium?.copyWith(
                          fontSize: AppSize.sp13,
                          color: context.themeColors.darkGreyColor),
                      titlePadding: 0,
                      needDivider: false,
                    )
                  } else ...{
                    Container()
                  },
                  SpaceV(AppSize.h20),
                  (scheduleMonthData?.openService != null &&
                          scheduleMonthData?.openService?.subscribed == false)
                      ? Center(
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: AppSize.w90,
                              right: AppSize.w90,
                            ),
                            child: CommonButton(
                              height: AppSize.h36,
                              titleStyle: context.textTheme.bodyMedium
                                  ?.copyWith(
                                      fontSize: AppSize.sp14,
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 1),
                              title: AppLocalizations.of(context)!.subscribe,
                              buttonColor: AppColors.primaryColor,
                              onPressed: () {
                                scheduleBloc
                                    .toggleMyOpenServiceSubscriptionApiData(
                                  context: context,
                                  baseServiceId: scheduleMonthData
                                          ?.openService?.baseServiceId ??
                                      '',
                                  departmentId: scheduleMonthData
                                          ?.openService?.departmentId ??
                                      '',
                                  date: scheduleMonthData?.date ?? '',
                                  personId: scheduleMonthData?.personId ?? '',
                                  isSubscribe: true,
                                )
                                  ..then((value) => Navigator.pop(context))
                                  ..then((value) => scheduleBloc.fetchMonthData(
                                      context,
                                      isApiCall: true));
                              },
                            ),
                          ),
                        )
                      : (scheduleMonthData?.openService != null &&
                              scheduleMonthData?.openService?.subscribed ==
                                  true)
                          ? Center(
                              child: Padding(
                                padding: EdgeInsets.only(
                                  left: AppSize.w90,
                                  right: AppSize.w90,
                                ),
                                child: CommonButton(
                                  height: AppSize.h36,
                                  title:
                                      AppLocalizations.of(context)!.unsubscribe,
                                  buttonColor: AppColors.lightModeRedColor,
                                  titleStyle: context.textTheme.bodyMedium
                                      ?.copyWith(
                                          fontSize: AppSize.sp14,
                                          color: AppColors.white,
                                          fontWeight: FontWeight.w500,
                                          letterSpacing: 1),
                                  onPressed: () {
                                    scheduleBloc
                                        .toggleMyOpenServiceSubscriptionApiData(
                                      context: context,
                                      baseServiceId: scheduleMonthData
                                              ?.openService?.baseServiceId ??
                                          '',
                                      departmentId: scheduleMonthData
                                              ?.openService?.departmentId ??
                                          '',
                                      date: scheduleMonthData?.date ?? '',
                                      personId:
                                          scheduleMonthData?.personId ?? '',
                                      isSubscribe: false,
                                    )
                                      ..then((value) => Navigator.pop(context))
                                      ..then((value) =>
                                          scheduleBloc.fetchMonthData(context,
                                              isApiCall: true));
                                  },
                                ),
                              ),
                            )
                          : scheduleMonthData?.swap != null &&
                                  scheduleMonthData?.swap?.stateId == null
                              ? Padding(
                                  padding: EdgeInsets.only(
                                    left: AppSize.w110,
                                    right: AppSize.w110,
                                  ),
                                  child: ValueListenableBuilder<bool>(
                                    valueListenable:
                                        scheduleBloc.isSetSwapShiftLoading,
                                    builder: (context, isSetSwapShiftLoading,
                                        child) {
                                      return CommonButton(
                                        height: AppSize.h36,
                                        isLoading: isSetSwapShiftLoading,
                                        title: AppLocalizations.of(context)!
                                            .swapShift,
                                        titleStyle: context.textTheme.bodyMedium
                                            ?.copyWith(
                                                fontSize: AppSize.sp14,
                                                color: AppColors.white,
                                                fontWeight: FontWeight.w500,
                                                letterSpacing: 1),
                                        onPressed: () {
                                          scheduleBloc.swapNoteController
                                              .clear();
                                          showDialog(
                                            context: context,
                                            builder: (dialogContext) {
                                              return AlertDialog(
                                                backgroundColor: context
                                                    .themeColors.cardColor,
                                                title: Text(
                                                    AppLocalizations.of(
                                                            context)!
                                                        .youAreAboutSwapShift,
                                                    style: context
                                                        .textTheme.headlineLarge
                                                        ?.copyWith(
                                                      fontSize: AppSize.sp18,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color: context.themeColors
                                                          .textColor,
                                                    )),
                                                content: TextField(
                                                  controller: scheduleBloc
                                                      .swapNoteController,
                                                  decoration: InputDecoration(
                                                    hintText:
                                                        AppLocalizations.of(
                                                                context)!
                                                            .noteForColleagues,
                                                    hintStyle: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      color: context
                                                          .themeColors.textColor
                                                          .withOpacity(0.5),
                                                    ),
                                                  ),
                                                ),
                                                actions: <Widget>[
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(
                                                                  dialogContext)
                                                              .pop(); // Close the dialog
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .noCancel
                                                              .toUpperCase(),
                                                          style: context
                                                              .textTheme
                                                              .headlineLarge
                                                              ?.copyWith(
                                                            fontSize:
                                                                AppSize.sp13,
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            color: context
                                                                .themeColors
                                                                .primaryColor,
                                                          ),
                                                        ),
                                                      ),
                                                      SpaceH(AppSize.w14),
                                                      TextButton(
                                                        onPressed: () async {
                                                          Navigator.of(
                                                                  dialogContext)
                                                              .pop();
                                                          scheduleBloc.setSwapAndServiceApiData(
                                                              context: context,
                                                              personId:
                                                                  scheduleMonthData
                                                                          ?.swap
                                                                          ?.personId ??
                                                                      '',
                                                              dateEntryId:
                                                                  scheduleMonthData
                                                                          ?.swap
                                                                          ?.dateEntryId ??
                                                                      '',
                                                              stateId: scheduleMonthData
                                                                      ?.swap
                                                                      ?.stateId ??
                                                                  '',
                                                              nextStateId:
                                                                  '77151add-11f2-432a-a7f6-2b65bedcc6db',
                                                              remark: scheduleBloc
                                                                  .swapNoteController
                                                                  .text)
                                                            ..then((value) =>
                                                                Navigator.pop(
                                                                    context))
                                                            ..then((value) =>
                                                                scheduleBloc
                                                                    .fetchMonthData(
                                                                        context,
                                                                        isApiCall:
                                                                            true));
                                                        },
                                                        child: Text(
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .yesSwapShift
                                                              .toUpperCase(),
                                                          style: context
                                                              .textTheme
                                                              .headlineLarge
                                                              ?.copyWith(
                                                            fontSize:
                                                                AppSize.sp13,
                                                            fontWeight:
                                                                FontWeight
                                                                    .normal,
                                                            color: context
                                                                .themeColors
                                                                .primaryColor,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  )
                                                ],
                                              );
                                            },
                                          );
                                        },
                                      );
                                    },
                                  ),
                                )
                              : (scheduleMonthData?.swap?.state ==
                                      'Aangevraagd')
                                  ? Center(
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          left: AppSize.w90,
                                          right: AppSize.w90,
                                        ),
                                        child: ValueListenableBuilder<bool>(
                                          valueListenable: scheduleBloc
                                              .isCancelSwapShiftLoading,
                                          builder: (context,
                                              isCancelSwapShiftLoading, child) {
                                            return CommonButton(
                                              height: AppSize.h36,
                                              isLoading:
                                                  isCancelSwapShiftLoading,
                                              titleStyle: context
                                                  .textTheme.bodyMedium
                                                  ?.copyWith(
                                                      fontSize: AppSize.sp14,
                                                      color: AppColors.white,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      letterSpacing: 1),
                                              title:
                                                  AppLocalizations.of(context)!
                                                      .cancelSwap
                                                      .toUpperCase(),
                                              buttonColor:
                                                  AppColors.lightModeRedColor,
                                              onPressed: () {
                                                scheduleBloc
                                                    .cancelSwapAndServiceApiData(
                                                  context: context,
                                                  personId: scheduleMonthData
                                                          ?.swap?.personId ??
                                                      '',
                                                  dateEntryId: scheduleMonthData
                                                          ?.swap?.dateEntryId ??
                                                      '',
                                                  stateId: scheduleMonthData
                                                          ?.swap?.stateId ??
                                                      '',
                                                  nextStateId: '',
                                                )
                                                  ..then((value) =>
                                                      Navigator.pop(context))
                                                  ..then((value) => scheduleBloc
                                                      .fetchMonthData(context,
                                                          isApiCall: true));
                                              },
                                            );
                                          },
                                        ),
                                      ),
                                    )
                                  : (scheduleMonthData?.swap?.state ==
                                          'Geaccepteerd')
                                      ? Text(
                                          AppLocalizations.of(context)!
                                              .youHaveRegistered,
                                          overflow: TextOverflow.ellipsis,
                                          style: context.textTheme.titleMedium
                                              ?.copyWith(
                                                  fontSize: AppSize.sp14,
                                                  color:
                                                      AppColors.primaryColor),
                                        )
                                      : (scheduleMonthData?.swap?.state ==
                                              'Uitgezet')
                                          ? Column(
                                              children: [
                                                Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    CommonButton(
                                                      height: AppSize.h36,
                                                      title:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .refuse
                                                              .toUpperCase(),
                                                      buttonColor: AppColors
                                                          .lightModeRedColor,
                                                      titleStyle: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                              fontSize:
                                                                  AppSize.sp14,
                                                              color: AppColors
                                                                  .white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              letterSpacing: 1),
                                                      width: AppSize.w80,
                                                      onPressed: () {
                                                        scheduleBloc
                                                            .cancelSwapAndServiceApiData(
                                                          context: context,
                                                          personId:
                                                              scheduleMonthData
                                                                      ?.swap
                                                                      ?.personId ??
                                                                  '',
                                                          dateEntryId:
                                                              scheduleMonthData
                                                                      ?.swap
                                                                      ?.dateEntryId ??
                                                                  '',
                                                          stateId:
                                                              scheduleMonthData
                                                                      ?.swap
                                                                      ?.stateId ??
                                                                  '',
                                                          nextStateId:
                                                              '40a784ce-78eb-44be-8a98-76806c2d24c9',
                                                        )
                                                          ..then((value) =>
                                                              Navigator.pop(
                                                                  context))
                                                          ..then((value) =>
                                                              scheduleBloc
                                                                  .fetchMonthData(
                                                                      context));
                                                      },
                                                    ),
                                                    CommonButton(
                                                      height: AppSize.h36,
                                                      title:
                                                          AppLocalizations.of(
                                                                  context)!
                                                              .registerShift
                                                              .toUpperCase(),
                                                      buttonColor: AppColors
                                                          .primaryColor,
                                                      titleStyle: context
                                                          .textTheme.bodyMedium
                                                          ?.copyWith(
                                                              fontSize:
                                                                  AppSize.sp14,
                                                              color: AppColors
                                                                  .white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              letterSpacing: 1),
                                                      width: AppSize.w200,
                                                      onPressed: () {
                                                        scheduleBloc
                                                            .cancelSwapAndServiceApiData(
                                                          context: context,
                                                          personId: '',
                                                          dateEntryId:
                                                              scheduleMonthData
                                                                      ?.swap
                                                                      ?.dateEntryId ??
                                                                  '',
                                                          stateId:
                                                              scheduleMonthData
                                                                      ?.swap
                                                                      ?.stateId ??
                                                                  '',
                                                          nextStateId:
                                                              '1ace5656-d483-4c5a-b561-0ebd583f6cf3',
                                                        )
                                                          ..then((value) =>
                                                              Navigator.pop(
                                                                  context))
                                                          ..then((value) =>
                                                              scheduleBloc
                                                                  .fetchMonthData(
                                                                      context));
                                                      },
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            )
                                          : Container(),
                  SpaceV(AppSize.h30),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
