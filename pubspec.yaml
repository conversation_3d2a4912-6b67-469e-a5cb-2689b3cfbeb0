name: staff_medewerker
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.

#IOS
# version: 1.1.1+3

#android
version: 1.1.3+23

environment:
  sdk: '>=3.1.2 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter  


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  
  shimmer: ^3.0.0
  internet_connection_checker: ^3.0.1
  animated_splash_screen: ^1.3.0
  cached_network_image: ^3.2.3
  intl: ^0.20.2
  http: ^1.1.0
  dio: ^5.3.3
  flutter_platform_widgets: ^9.0.0
  flutter_screenutil: ^5.8.4
  flutter_svg: ^2.0.9
  provider: ^6.0.5
  flutter_sticky_header: ^0.8.0
  isoweek: ^1.1.4
  time: ^2.1.3
  scroll_date_picker: ^3.7.3
  url_launcher: ^6.1.14
  flutter_animate: ^4.2.0+1
  flutter_native_splash: ^2.3.2
  image_picker: ^1.0.4
  flutter_bloc: ^9.1.1
  ionicons: ^0.2.2
  equatable: ^2.0.5
  qr_code_scanner_plus: ^2.0.10+1
  device_info_plus: ^11.5.0
  table_calendar: ^3.0.9
  lottie: ^3.3.1
  syncfusion_flutter_datepicker: ^30.1.38
  syncfusion_flutter_calendar: ^30.1.38
  package_info_plus: ^8.3.0
  flutter_html: ^3.0.0-beta.2
  scrollable_positioned_list: ^0.3.8
  path_provider: ^2.1.1
  system_info2: ^4.0.0

  #  very_good_analysis: ^5.1.0
  icalendar_parser: ^2.0.0
  persistent_bottom_nav_bar: ^6.2.1

  flutter_local_notifications: ^19.3.0


  super_tooltip: ^2.0.7
  permission_handler: ^12.0.1
  open_file: ^3.3.2
  flutter_widget_from_html: ^0.16.0

  # Local Storage
  shared_preferences: ^2.2.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.0
  build_runner: ^2.3.0
  flutter_html_iframe: ^3.0.0-alpha.4
  flutter_html_audio: ^3.0.0-beta.2
  skeletonizer: ^2.1.0
  

dev_dependencies:
  flutter_test:
    sdk: flutter
  change_app_package_name: ^1.1.0

flutter_native_splash:
  color: "#2A2A2A"
  android_12:
    color: "#2A2A2A"
#    image: assets/images/splash_logo.png
  ios: false

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1
  rename: ^2.1.1
  

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
     - assets/images/
     - assets/icons/
     - assets/logos/
     - assets/gif/

  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have aF
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
